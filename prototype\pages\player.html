<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放器 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">

    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>

    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        /* 播放器特殊样式 */
        .player-container {
            background: linear-gradient(135deg, 
                var(--bg-primary) 0%, 
                var(--bg-secondary) 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }
        
        .album-cover-large {
            width: 280px;
            height: 280px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            position: relative;
            overflow: hidden;
        }
        
        .album-cover-large.playing {
            animation: rotate 20s linear infinite;
        }
        
        .album-cover-large::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 40px;
            height: 40px;
            background: var(--card-bg);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;
        }
        
        .album-cover-large::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 8px;
            height: 8px;
            background: var(--text-secondary);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            z-index: 3;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .lyrics-container {
            max-height: 200px;
            overflow-y: auto;
            scrollbar-width: none;
            -ms-overflow-style: none;
        }
        
        .lyrics-container::-webkit-scrollbar {
            display: none;
        }
        
        .lyric-line {
            padding: 0.5rem 0;
            transition: all 0.3s ease;
            text-align: center;
            opacity: 0.5;
        }
        
        .lyric-line.current {
            opacity: 1;
            color: var(--color-accent);
            font-weight: 600;
            transform: scale(1.05);
        }
        
        .progress-container {
            position: relative;
            height: 6px;
            background: var(--border-color);
            border-radius: 3px;
            cursor: pointer;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            border-radius: 3px;
            position: relative;
            transition: width 0.3s ease;
        }
        
        .progress-thumb {
            position: absolute;
            top: 50%;
            right: -8px;
            width: 16px;
            height: 16px;
            background: var(--color-accent);
            border-radius: 50%;
            transform: translateY(-50%);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .progress-container:hover .progress-thumb {
            opacity: 1;
        }
        
        .control-button {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-md);
        }
        
        .control-button:hover {
            transform: scale(1.1);
            box-shadow: var(--shadow-lg);
        }
        
        .play-button {
            width: 80px;
            height: 80px;
            background: var(--color-primary);
            color: white;
            border: none;
        }
        
        .play-button:hover {
            background: var(--color-accent);
        }
        
        .floating-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 30% 20%, 
                rgba(74, 144, 226, 0.1) 0%, 
                transparent 50%),
                radial-gradient(circle at 70% 80%, 
                rgba(212, 175, 55, 0.1) 0%, 
                transparent 50%);
            pointer-events: none;
            animation: float-bg 8s ease-in-out infinite;
        }
        
        @keyframes float-bg {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 0.6; }
        }
    </style>
</head>
<body class="font-primary">
    <!-- 主题管理器 -->
    <script src="../assets/js/theme.js"></script>
    
    <!-- 播放器界面 -->
    <div class="player-container water-bg" x-data="playerApp()">
        <!-- 浮动背景 -->
        <div class="floating-background"></div>
        
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <button @click="goBack()" class="header-button" aria-label="返回">
                    <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                </button>

                <div class="header-actions">
                    <button @click="shareCurrentSong()" class="header-button" aria-label="分享">
                        <iconify-icon icon="material-symbols:share" class="text-lg"></iconify-icon>
                    </button>

                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>

                    <button @click="showMoreOptions()" class="header-button" aria-label="更多选项">
                        <iconify-icon icon="material-symbols:more-vert" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 主要内容区域 -->
        <main class="flex flex-col items-center px-6 py-8 relative z-10">
            <!-- 专辑封面 -->
            <div class="mb-8">
                <div class="album-cover-large" :class="{ 'playing': isPlaying }">
                    <iconify-icon icon="material-symbols:music-note" x-show="!currentSong?.cover"></iconify-icon>
                    <img :src="currentSong?.cover" :alt="currentSong?.album" 
                         class="w-full h-full object-cover rounded-full" 
                         x-show="currentSong?.cover">
                </div>
            </div>
            
            <!-- 歌曲信息 -->
            <div class="text-center mb-8 max-w-sm">
                <h2 class="text-2xl font-bold text-primary mb-2" x-text="currentSong?.title || '未选择歌曲'"></h2>
                <p class="text-lg text-secondary mb-1" x-text="currentSong?.artist || '未知艺术家'"></p>
                <p class="text-sm text-secondary opacity-75" x-text="currentSong?.album || '未知专辑'"></p>
            </div>
            
            <!-- 歌词区域 -->
            <div class="lyrics-container w-full max-w-md mb-8" x-show="lyrics.length > 0">
                <template x-for="(line, index) in lyrics" :key="index">
                    <div 
                        class="lyric-line"
                        :class="{ 'current': index === currentLyricIndex }"
                        x-text="line.text"
                    ></div>
                </template>
            </div>
            
            <!-- 无歌词提示 -->
            <div class="text-center text-secondary opacity-50 mb-8" x-show="lyrics.length === 0">
                <iconify-icon icon="material-symbols:lyrics" class="text-3xl mb-2"></iconify-icon>
                <p class="text-sm">暂无歌词</p>
            </div>
        </main>
        
        <!-- 底部控制区域 -->
        <footer class="player-bottom-control glass-morphism">
            <!-- 进度条区域 -->
            <div class="progress-section">
                <div class="progress-container" @click="seekTo($event)">
                    <div class="progress-fill" :style="`width: ${playProgress}%`">
                        <div class="progress-thumb"></div>
                    </div>
                </div>
                <div class="progress-time">
                    <span x-text="formatTime(currentTime)">00:00</span>
                    <span x-text="formatTime(currentSong?.duration || 0)">00:00</span>
                </div>
            </div>

            <!-- 主要播放控制 -->
            <div class="player-controls-section">
                <div class="player-control-buttons">
                    <button @click="toggleShuffle()"
                            class="player-control-button"
                            :class="isShuffled ? 'active' : ''">
                        <iconify-icon icon="material-symbols:shuffle" class="text-xl"></iconify-icon>
                    </button>

                    <button @click="previousSong()" class="player-control-button">
                        <iconify-icon icon="material-symbols:skip-previous" class="text-2xl"></iconify-icon>
                    </button>

                    <button @click="togglePlay()" class="player-control-button play">
                        <iconify-icon :icon="isPlaying ? 'material-symbols:pause' : 'material-symbols:play-arrow'" class="text-3xl"></iconify-icon>
                    </button>

                    <button @click="nextSong()" class="player-control-button">
                        <iconify-icon icon="material-symbols:skip-next" class="text-2xl"></iconify-icon>
                    </button>

                    <button @click="toggleRepeat()"
                            class="player-control-button"
                            :class="repeatMode !== 'none' ? 'active' : ''">
                        <iconify-icon :icon="repeatMode === 'one' ? 'material-symbols:repeat-one' : 'material-symbols:repeat'" class="text-xl"></iconify-icon>
                    </button>
                </div>
            </div>

            <!-- 次要控制 -->
            <div class="player-secondary-controls">
                <button @click="toggleFavorite()" class="header-button" :class="currentSong?.isFavorite ? 'active' : ''">
                    <iconify-icon :icon="currentSong?.isFavorite ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'"
                                  class="text-xl"></iconify-icon>
                </button>

                <div class="volume-control">
                    <iconify-icon icon="material-symbols:volume-down" class="text-lg text-secondary"></iconify-icon>
                    <div class="volume-slider">
                        <div class="volume-fill" :style="`width: ${volume}%`"></div>
                    </div>
                    <iconify-icon icon="material-symbols:volume-up" class="text-lg text-secondary"></iconify-icon>
                </div>
            </div>
        </footer>
    </div>
    
    <script>
        function playerApp() {
            return {
                // 播放状态
                isPlaying: false,
                currentTime: 0,
                playProgress: 0,
                volume: 75,
                isShuffled: false,
                repeatMode: 'none',
                
                // 歌曲信息
                currentSong: {
                    id: 1,
                    title: '水调歌头',
                    artist: '古风音乐',
                    album: '诗词歌赋',
                    duration: 245,
                    isFavorite: true,
                    cover: null
                },
                
                // 歌词
                lyrics: [
                    { time: 0, text: '明月几时有' },
                    { time: 5, text: '把酒问青天' },
                    { time: 10, text: '不知天上宫阙' },
                    { time: 15, text: '今夕是何年' },
                    { time: 20, text: '我欲乘风归去' },
                    { time: 25, text: '又恐琼楼玉宇' },
                    { time: 30, text: '高处不胜寒' }
                ],
                
                currentLyricIndex: 0,
                
                togglePlay() {
                    this.isPlaying = !this.isPlaying;
                    console.log(this.isPlaying ? '▶️ 播放' : '⏸️ 暂停');
                },
                
                previousSong() {
                    console.log('⏮️ 上一首');
                },
                
                nextSong() {
                    console.log('⏭️ 下一首');
                },
                
                toggleFavorite() {
                    if (this.currentSong) {
                        this.currentSong.isFavorite = !this.currentSong.isFavorite;
                        console.log(this.currentSong.isFavorite ? '⚓ 已收藏' : '○ 取消收藏');
                    }
                },
                
                toggleShuffle() {
                    this.isShuffled = !this.isShuffled;
                    console.log(this.isShuffled ? '🔀 随机播放' : '📋 顺序播放');
                },
                
                toggleRepeat() {
                    const modes = ['none', 'all', 'one'];
                    const currentIndex = modes.indexOf(this.repeatMode);
                    this.repeatMode = modes[(currentIndex + 1) % modes.length];
                    console.log('🔁 重复模式:', this.repeatMode);
                },
                
                seekTo(event) {
                    const rect = event.currentTarget.getBoundingClientRect();
                    const percent = (event.clientX - rect.left) / rect.width;
                    this.currentTime = percent * this.currentSong.duration;
                    this.playProgress = percent * 100;
                    console.log('⏩ 跳转到:', this.formatTime(this.currentTime));
                },
                
                formatTime(seconds) {
                    const mins = Math.floor(seconds / 60);
                    const secs = Math.floor(seconds % 60);
                    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                },
                
                goBack() {
                    window.history.back();
                },
                
                shareCurrentSong() {
                    console.log('📤 分享歌曲:', this.currentSong?.title);
                },
                
                showMoreOptions() {
                    console.log('⋮ 更多选项');
                },
                
                updateLyrics() {
                    // 根据当前播放时间更新歌词高亮
                    for (let i = this.lyrics.length - 1; i >= 0; i--) {
                        if (this.currentTime >= this.lyrics[i].time) {
                            this.currentLyricIndex = i;
                            break;
                        }
                    }
                },
                
                init() {
                    console.log('🌊 播放器界面已加载');
                    
                    // 模拟播放进度
                    setInterval(() => {
                        if (this.isPlaying && this.currentSong) {
                            this.currentTime += 1;
                            this.playProgress = (this.currentTime / this.currentSong.duration) * 100;
                            this.updateLyrics();
                            
                            if (this.currentTime >= this.currentSong.duration) {
                                this.nextSong();
                            }
                        }
                    }, 1000);
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 播放器界面');
        });
    </script>
</body>
</html>
