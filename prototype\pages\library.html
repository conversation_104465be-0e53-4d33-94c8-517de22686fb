<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐宝库 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .library-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .category-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            text-align: center;
            min-height: 160px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .category-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .category-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .category-card:hover::before {
            opacity: 1;
        }
        
        .category-icon {
            font-size: 3.5rem;
            color: var(--color-primary);
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        .category-card:hover .category-icon {
            transform: scale(1.1);
            color: var(--color-accent);
        }

        .category-title {
            font-size: 1.375rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 0.75rem;
        }

        .category-count {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .stats-overview {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
        }
        
        .water-level {
            width: 100%;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            position: relative;
        }
        
        .water-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            border-radius: 4px;
            transition: width 0.8s ease;
            position: relative;
        }
        
        .water-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: wave 2s ease-in-out infinite;
        }
        
        @keyframes wave {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="library-container water-bg" x-data="libraryApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">音乐宝库</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="scanLibrary()" class="header-button" aria-label="扫描音乐库">
                        <iconify-icon icon="material-symbols:refresh" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 音乐库统计 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <div class="stats-overview">
                    <h2 class="title-lg text-primary mb-4">音乐库概览</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="libraryStats.totalSongs"></div>
                            <div class="text-sm text-secondary">歌曲</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="libraryStats.totalAlbums"></div>
                            <div class="text-sm text-secondary">专辑</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="libraryStats.totalArtists"></div>
                            <div class="text-sm text-secondary">艺术家</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="formatSize(libraryStats.totalSize)"></div>
                            <div class="text-sm text-secondary">容量</div>
                        </div>
                    </div>
                    
                    <div class="mb-2">
                        <div class="flex justify-between text-sm text-secondary mb-1">
                            <span>存储使用情况</span>
                            <span x-text="Math.round(libraryStats.usagePercent) + '%'"></span>
                        </div>
                        <div class="water-level">
                            <div class="water-fill" :style="`width: ${libraryStats.usagePercent}%`"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 分类导航 -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                    <div @click="navigateTo('./song-list.html')" class="category-card">
                        <iconify-icon icon="material-symbols:music-note" class="category-icon"></iconify-icon>
                        <div class="category-title">歌曲</div>
                        <div class="category-count" x-text="libraryStats.totalSongs + ' 首'"></div>
                    </div>
                    
                    <div @click="navigateTo('./album.html')" class="category-card">
                        <iconify-icon icon="material-symbols:album" class="category-icon"></iconify-icon>
                        <div class="category-title">专辑</div>
                        <div class="category-count" x-text="libraryStats.totalAlbums + ' 张'"></div>
                    </div>
                    
                    <div @click="navigateTo('./artist.html')" class="category-card">
                        <iconify-icon icon="material-symbols:person" class="category-icon"></iconify-icon>
                        <div class="category-title">艺术家</div>
                        <div class="category-count" x-text="libraryStats.totalArtists + ' 位'"></div>
                    </div>
                    
                    <div @click="navigateTo('./playlist.html')" class="category-card">
                        <iconify-icon icon="material-symbols:playlist-play" class="category-icon"></iconify-icon>
                        <div class="category-title">播放列表</div>
                        <div class="category-count" x-text="libraryStats.totalPlaylists + ' 个'"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function libraryApp() {
            return {
                libraryStats: {
                    totalSongs: 1247,
                    totalAlbums: 156,
                    totalArtists: 89,
                    totalPlaylists: 12,
                    totalSize: 8.7 * 1024 * 1024 * 1024, // 8.7GB
                    usagePercent: 65
                },
                
                formatSize(bytes) {
                    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
                    if (bytes === 0) return '0 B';
                    const i = Math.floor(Math.log(bytes) / Math.log(1024));
                    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
                },
                
                navigateTo(url) {
                    window.location.href = url;
                },
                
                scanLibrary() {
                    console.log('🔄 扫描音乐库');
                    // 模拟扫描过程
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('📚 音乐库页面已加载');
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 音乐库');
        });
    </script>
</body>
</html>
