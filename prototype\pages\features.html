<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能介绍 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .features-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
            padding: 4rem 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .hero-section::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 4s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .hero-content {
            position: relative;
            z-index: 1;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .hero-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .hero-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .feature-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            border-radius: 20px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2.5rem;
            margin-bottom: 1.5rem;
            position: relative;
        }
        
        .feature-icon::after {
            content: '';
            position: absolute;
            inset: -2px;
            border-radius: 22px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .feature-card:hover .feature-icon::after {
            opacity: 0.3;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }
        
        .feature-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .feature-highlights {
            list-style: none;
            padding: 0;
        }
        
        .feature-highlights li {
            position: relative;
            padding-left: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .feature-highlights li::before {
            content: '✨';
            position: absolute;
            left: 0;
        }
        
        .showcase-section {
            background: var(--card-bg);
            border-radius: 24px;
            padding: 3rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin: 3rem 0;
            position: relative;
            overflow: hidden;
        }
        
        .showcase-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .showcase-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
        }
        
        .showcase-content h3 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }
        
        .showcase-content p {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 1.5rem;
        }
        
        .showcase-image {
            width: 100%;
            height: 300px;
            border-radius: 16px;
            background: linear-gradient(135deg, var(--color-primary)/20, var(--color-accent)/20);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-primary);
            font-size: 4rem;
            border: 2px solid var(--border-color);
        }
        
        .cta-section {
            text-align: center;
            padding: 3rem;
            background: linear-gradient(135deg, var(--color-primary)/10, var(--color-accent)/10);
            border-radius: 24px;
            border: 1px solid var(--color-primary)/20;
            margin: 3rem 0;
        }
        
        .cta-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }
        
        .cta-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .cta-buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }
        
        .cta-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            text-decoration: none;
        }
        
        .cta-button.primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
        }
        
        .cta-button.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
        }
        
        .cta-button.secondary {
            background: var(--card-bg);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .cta-button.secondary:hover {
            background: var(--bg-secondary);
            transform: translateY(-2px);
        }
        
        .stats-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 2rem;
            background: var(--card-bg);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="features-container" x-data="featuresApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">功能介绍</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="shareFeatures()" class="header-button" aria-label="分享">
                        <iconify-icon icon="material-symbols:share" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 英雄区域 -->
        <div class="hero-section">
            <div class="hero-content">
                <h1 class="hero-title">听汀</h1>
                <p class="hero-subtitle">水边的静谧，音乐的停泊</p>
                <p>一款专注于纯粹音乐体验的播放器，融合中国风美学与现代设计</p>
            </div>
        </div>
        
        <!-- 功能内容 -->
        <div class="px-6 py-8">
            <div class="max-w-6xl mx-auto">
                <!-- 核心功能 -->
                <div class="feature-grid">
                    <template x-for="feature in coreFeatures" :key="feature.id">
                        <div @click="showFeatureDetail(feature)" class="feature-card">
                            <div class="feature-icon">
                                <iconify-icon :icon="feature.icon"></iconify-icon>
                            </div>
                            <h3 class="feature-title" x-text="feature.title"></h3>
                            <p class="feature-description" x-text="feature.description"></p>
                            <ul class="feature-highlights">
                                <template x-for="highlight in feature.highlights" :key="highlight">
                                    <li x-text="highlight"></li>
                                </template>
                            </ul>
                        </div>
                    </template>
                </div>
                
                <!-- 特色展示 -->
                <div class="showcase-section">
                    <div class="showcase-grid">
                        <div class="showcase-content">
                            <h3>中国风设计语言</h3>
                            <p>融合传统美学与现代设计，打造独特的视觉体验。毛玻璃质感、水波动效、诗意命名，每一个细节都体现着东方美学的温润与雅致。</p>
                            <ul class="feature-highlights">
                                <li>锚点隐喻：收藏如船锚，情感的停泊</li>
                                <li>水波反馈：交互如涟漪，自然而流畅</li>
                                <li>温度感知：界面随心境变化</li>
                            </ul>
                        </div>
                        <div class="showcase-image">
                            <iconify-icon icon="material-symbols:palette"></iconify-icon>
                        </div>
                    </div>
                </div>
                
                <!-- 数据统计 -->
                <div class="stats-section">
                    <div class="stat-item">
                        <div class="stat-number">17+</div>
                        <div class="stat-label">功能页面</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">无广告</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">10+</div>
                        <div class="stat-label">音频格式</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24/7</div>
                        <div class="stat-label">本地播放</div>
                    </div>
                </div>
                
                <!-- 行动号召 -->
                <div class="cta-section">
                    <h2 class="cta-title">开始你的音乐之旅</h2>
                    <p class="cta-description">
                        体验听汀带来的纯粹音乐享受，让每一首歌都成为心灵的停泊港湾。
                        无广告、无推送、无网络依赖，只有音乐和你。
                    </p>
                    <div class="cta-buttons">
                        <a href="./main.html" class="cta-button primary">
                            <iconify-icon icon="material-symbols:play-arrow" class="text-lg"></iconify-icon>
                            立即体验
                        </a>
                        <a href="./help.html" class="cta-button secondary">
                            <iconify-icon icon="material-symbols:help" class="text-lg"></iconify-icon>
                            了解更多
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function featuresApp() {
            return {
                coreFeatures: [
                    {
                        id: 1,
                        icon: 'material-symbols:music-note',
                        title: '纯粹音乐播放',
                        description: '专注于本地音乐播放，无广告、无推送、无网络依赖，只有纯粹的音乐体验。',
                        highlights: [
                            '支持多种音频格式',
                            '高品质音频输出',
                            '智能音乐库管理',
                            '无损音质保证'
                        ]
                    },
                    {
                        id: 2,
                        icon: 'material-symbols:palette',
                        title: '中国风设计',
                        description: '融合传统美学与现代设计，打造独特的视觉体验和情感共鸣。',
                        highlights: [
                            '毛玻璃质感界面',
                            '水波动效反馈',
                            '诗意功能命名',
                            '温度感知主题'
                        ]
                    },
                    {
                        id: 3,
                        icon: 'material-symbols:anchor',
                        title: '情感化收藏',
                        description: '独创的"锚点"收藏系统，让每一首歌都成为情感的停泊港湾。',
                        highlights: [
                            '锚点隐喻设计',
                            '情感标签分类',
                            '时间轴记录',
                            '心情色彩标记'
                        ]
                    },
                    {
                        id: 4,
                        icon: 'material-symbols:equalizer',
                        title: '专业音效',
                        description: '内置专业级均衡器和音效增强，为不同音乐类型提供最佳听感。',
                        highlights: [
                            '10段均衡器',
                            '多种预设模式',
                            '3D环绕音效',
                            '低音增强'
                        ]
                    },
                    {
                        id: 5,
                        icon: 'material-symbols:playlist-play',
                        title: '智能播放列表',
                        description: '强大的播放列表管理功能，支持拖拽排序、智能分类和个性化推荐。',
                        highlights: [
                            '拖拽排序',
                            '智能分类',
                            '批量操作',
                            '自动备份'
                        ]
                    },
                    {
                        id: 6,
                        icon: 'material-symbols:space-dashboard',
                        title: '个人音乐空间',
                        description: '可视化展示你的音乐品味，生成专属的音乐画像和统计分析。',
                        highlights: [
                            '品味雷达图',
                            '听歌足迹',
                            '音乐成就',
                            '分享名片'
                        ]
                    }
                ],
                
                showFeatureDetail(feature) {
                    console.log('📋 查看功能详情:', feature.title);
                    // 这里可以实现功能详情展示
                },
                
                shareFeatures() {
                    console.log('📤 分享功能介绍');
                    if (navigator.share) {
                        navigator.share({
                            title: '听汀 - 功能介绍',
                            text: '水边的静谧，音乐的停泊',
                            url: window.location.href
                        });
                    } else {
                        // 复制链接到剪贴板
                        navigator.clipboard.writeText(window.location.href);
                        alert('链接已复制到剪贴板');
                    }
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('🎨 功能介绍页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 功能介绍');
        });
    </script>
</body>
</html>
