<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>歌曲列表 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">

    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>

    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        /* 歌曲列表特殊样式 */
        .list-container {
            background: linear-gradient(135deg, 
                var(--bg-primary) 0%, 
                var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .song-item {
            transition: all 0.2s ease;
            border-radius: var(--radius-md);
            position: relative;
            overflow: hidden;
        }
        
        .song-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .song-item:hover::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--color-accent);
        }
        
        .song-item.playing {
            background: var(--bg-secondary);
            border-left: 3px solid var(--color-primary);
        }
        
        .song-item.playing .song-title {
            color: var(--color-primary);
        }
        
        .album-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
            gap: 1rem;
        }
        
        .album-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            padding: 1rem;
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .album-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }
        
        .album-cover {
            width: 100%;
            aspect-ratio: 1;
            border-radius: var(--radius-md);
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin-bottom: 0.75rem;
        }
        
        .search-bar {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            padding: 0.75rem 1rem;
            width: 100%;
            color: var(--text-primary);
            transition: all 0.3s ease;
        }
        
        .search-bar:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }
        
        .filter-chip {
            padding: 0.5rem 1rem;
            border-radius: var(--radius-full);
            background: var(--bg-secondary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .filter-chip.active {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }
        
        .sort-dropdown {
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            position: absolute;
            top: 100%;
            right: 0;
            min-width: 160px;
            z-index: 1000;
        }
        
        .sort-option {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        
        .sort-option:hover {
            background: var(--bg-secondary);
        }
        
        .sort-option.active {
            background: var(--color-primary);
            color: white;
        }
    </style>
</head>
<body class="font-primary">
    <!-- 主题管理器 -->
    <script src="../assets/js/theme.js"></script>
    
    <!-- 歌曲列表页面 -->
    <div class="list-container water-bg" x-data="songListApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism sticky top-0 z-50">
            <div class="flex items-center justify-between p-6">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">歌曲列表</h1>
                </div>

                <div class="header-actions">
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>

                    <div class="relative">
                        <button @click="showSortMenu = !showSortMenu" class="header-button" aria-label="排序">
                            <iconify-icon icon="material-symbols:sort" class="text-lg"></iconify-icon>
                        </button>

                        <!-- 排序下拉菜单 -->
                        <div x-show="showSortMenu" @click.away="showSortMenu = false"
                             x-transition class="sort-dropdown">
                            <div @click="setSortBy('title')"
                                 :class="sortBy === 'title' ? 'active' : ''"
                                 class="sort-option">
                                按标题排序
                            </div>
                            <div @click="setSortBy('artist')"
                                 :class="sortBy === 'artist' ? 'active' : ''"
                                 class="sort-option">
                                按艺术家排序
                            </div>
                            <div @click="setSortBy('album')"
                                 :class="sortBy === 'album' ? 'active' : ''"
                                 class="sort-option">
                                按专辑排序
                            </div>
                            <div @click="setSortBy('duration')"
                                 :class="sortBy === 'duration' ? 'active' : ''"
                                 class="sort-option">
                                按时长排序
                            </div>
                            <div @click="setSortBy('dateAdded')"
                                 :class="sortBy === 'dateAdded' ? 'active' : ''"
                                 class="sort-option">
                                按添加时间排序
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 搜索栏 -->
            <div class="px-6 pb-4">
                <div class="relative">
                    <iconify-icon icon="material-symbols:search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-secondary"></iconify-icon>
                    <input 
                        type="text" 
                        placeholder="搜索歌曲、艺术家或专辑..."
                        x-model="searchQuery"
                        class="search-bar pl-10"
                    >
                </div>
            </div>
            
            <!-- 筛选标签 -->
            <div class="px-6 pb-4">
                <div class="flex space-x-3 overflow-x-auto">
                    <button @click="setFilter('all')" 
                            :class="currentFilter === 'all' ? 'active' : ''" 
                            class="filter-chip whitespace-nowrap">
                        全部
                    </button>
                    <button @click="setFilter('favorites')" 
                            :class="currentFilter === 'favorites' ? 'active' : ''" 
                            class="filter-chip whitespace-nowrap">
                        收藏
                    </button>
                    <button @click="setFilter('recent')" 
                            :class="currentFilter === 'recent' ? 'active' : ''" 
                            class="filter-chip whitespace-nowrap">
                        最近播放
                    </button>
                    <button @click="setFilter('mostPlayed')" 
                            :class="currentFilter === 'mostPlayed' ? 'active' : ''" 
                            class="filter-chip whitespace-nowrap">
                        最常播放
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 视图切换 -->
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <button @click="viewMode = 'list'" 
                            :class="viewMode === 'list' ? 'text-primary' : 'text-secondary'" 
                            class="p-2 rounded-lg hover:bg-bg-secondary transition-colors">
                        <iconify-icon icon="material-symbols:list" class="text-xl"></iconify-icon>
                    </button>
                    <button @click="viewMode = 'grid'" 
                            :class="viewMode === 'grid' ? 'text-primary' : 'text-secondary'" 
                            class="p-2 rounded-lg hover:bg-bg-secondary transition-colors">
                        <iconify-icon icon="material-symbols:grid-view" class="text-xl"></iconify-icon>
                    </button>
                </div>
                
                <div class="text-sm text-secondary">
                    <span x-text="filteredSongs.length"></span> 首歌曲
                </div>
            </div>
        </div>
        
        <!-- 歌曲列表视图 -->
        <div x-show="viewMode === 'list'" class="px-6 pb-32">
            <div class="space-y-2">
                <template x-for="(song, index) in filteredSongs" :key="song.id">
                    <div 
                        @click="playSong(song)"
                        class="song-item p-4 cursor-pointer flex items-center space-x-4"
                        :class="currentSong?.id === song.id ? 'playing' : ''"
                    >
                        <!-- 序号或播放状态 -->
                        <div class="w-8 text-center flex-shrink-0">
                            <span x-show="currentSong?.id !== song.id" 
                                  class="text-sm text-secondary" 
                                  x-text="index + 1"></span>
                            <iconify-icon x-show="currentSong?.id === song.id && isPlaying" 
                                          icon="material-symbols:graphic-eq" 
                                          class="text-primary animate-pulse"></iconify-icon>
                            <iconify-icon x-show="currentSong?.id === song.id && !isPlaying" 
                                          icon="material-symbols:pause" 
                                          class="text-primary"></iconify-icon>
                        </div>
                        
                        <!-- 封面 -->
                        <div class="w-12 h-12 rounded-lg bg-gradient-to-br from-primary to-accent flex items-center justify-center text-white flex-shrink-0">
                            <iconify-icon icon="material-symbols:music-note" class="text-lg" x-show="!song.cover"></iconify-icon>
                            <img :src="song.cover" :alt="song.album" class="w-full h-full object-cover rounded-lg" x-show="song.cover">
                        </div>
                        
                        <!-- 歌曲信息 -->
                        <div class="flex-1 min-w-0">
                            <h4 class="font-medium song-title truncate" x-text="song.title"></h4>
                            <p class="text-sm text-secondary truncate">
                                <span x-text="song.artist"></span>
                                <span class="opacity-75"> - </span>
                                <span x-text="song.album" class="opacity-75"></span>
                            </p>
                        </div>
                        
                        <!-- 播放次数 -->
                        <div class="text-xs text-secondary flex-shrink-0" x-show="song.playCount > 0">
                            <span x-text="song.playCount"></span> 次
                        </div>
                        
                        <!-- 时长和收藏 -->
                        <div class="flex items-center space-x-3 flex-shrink-0">
                            <span class="text-xs text-secondary" x-text="formatTime(song.duration)"></span>
                            <button 
                                @click.stop="toggleFavorite(song)"
                                class="p-1 rounded hover:bg-bg-secondary transition-colors"
                            >
                                <iconify-icon 
                                    :icon="song.isFavorite ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'" 
                                    :class="song.isFavorite ? 'text-accent' : 'text-secondary'"
                                    class="text-lg"
                                ></iconify-icon>
                            </button>
                        </div>
                    </div>
                </template>
            </div>
        </div>
        
        <!-- 专辑网格视图 -->
        <div x-show="viewMode === 'grid'" class="px-6 pb-32">
            <div class="album-grid">
                <template x-for="album in albums" :key="album.name">
                    <div @click="playAlbum(album)" class="album-card cursor-pointer">
                        <div class="album-cover">
                            <iconify-icon icon="material-symbols:album" x-show="!album.cover"></iconify-icon>
                            <img :src="album.cover" :alt="album.name" class="w-full h-full object-cover rounded-md" x-show="album.cover">
                        </div>
                        <h4 class="font-medium text-primary truncate" x-text="album.name"></h4>
                        <p class="text-sm text-secondary truncate" x-text="album.artist"></p>
                        <p class="text-xs text-secondary opacity-75 mt-1">
                            <span x-text="album.songCount"></span> 首歌曲
                        </p>
                    </div>
                </template>
            </div>
        </div>
        
        <!-- 底部播放控制栏 -->
        <div class="bottom-control-container glass-morphism" x-show="currentSong">
            <!-- 迷你播放器卡片 -->
            <div class="mini-player-card">
                <div class="mini-player-cover">
                    <iconify-icon icon="material-symbols:music-note" x-show="!currentSong?.cover"></iconify-icon>
                    <img :src="currentSong?.cover" :alt="currentSong?.album" class="w-full h-full object-cover rounded-xl" x-show="currentSong?.cover">
                </div>

                <div class="mini-player-info">
                    <div class="mini-player-title" x-text="currentSong?.title || '未选择歌曲'"></div>
                    <div class="mini-player-artist" x-text="currentSong?.artist || '未知艺术家'"></div>
                </div>

                <div class="control-button-group">
                    <button @click="togglePlay()" class="control-button primary">
                        <iconify-icon :icon="isPlaying ? 'material-symbols:pause' : 'material-symbols:play-arrow'" class="text-lg"></iconify-icon>
                    </button>
                    <button @click="openPlayer()" class="control-button">
                        <iconify-icon icon="material-symbols:fullscreen" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function songListApp() {
            return {
                // 界面状态
                viewMode: 'list', // 'list' or 'grid'
                searchQuery: '',
                currentFilter: 'all',
                sortBy: 'title',
                showSortMenu: false,
                
                // 播放状态
                isPlaying: false,
                currentSong: null,
                
                // 示例数据
                songs: [
                    {
                        id: 1,
                        title: '水调歌头',
                        artist: '古风音乐',
                        album: '诗词歌赋',
                        duration: 245,
                        isFavorite: true,
                        playCount: 15,
                        dateAdded: '2024-01-15',
                        cover: null
                    },
                    {
                        id: 2,
                        title: '汀上白沙',
                        artist: '民谣歌手',
                        album: '江南印象',
                        duration: 198,
                        isFavorite: false,
                        playCount: 8,
                        dateAdded: '2024-01-20',
                        cover: null
                    },
                    {
                        id: 3,
                        title: '静夜思',
                        artist: '古典音乐',
                        album: '唐诗三百首',
                        duration: 167,
                        isFavorite: true,
                        playCount: 23,
                        dateAdded: '2024-01-10',
                        cover: null
                    },
                    {
                        id: 4,
                        title: '春江花月夜',
                        artist: '古风音乐',
                        album: '诗词歌赋',
                        duration: 312,
                        isFavorite: false,
                        playCount: 5,
                        dateAdded: '2024-01-25',
                        cover: null
                    },
                    {
                        id: 5,
                        title: '渔舟唱晚',
                        artist: '古筝演奏',
                        album: '古筝名曲',
                        duration: 278,
                        isFavorite: true,
                        playCount: 12,
                        dateAdded: '2024-01-18',
                        cover: null
                    }
                ],
                
                get albums() {
                    const albumMap = new Map();
                    this.songs.forEach(song => {
                        if (!albumMap.has(song.album)) {
                            albumMap.set(song.album, {
                                name: song.album,
                                artist: song.artist,
                                songCount: 0,
                                cover: null
                            });
                        }
                        albumMap.get(song.album).songCount++;
                    });
                    return Array.from(albumMap.values());
                },
                
                get filteredSongs() {
                    let filtered = this.songs;
                    
                    // 搜索过滤
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(song => 
                            song.title.toLowerCase().includes(query) ||
                            song.artist.toLowerCase().includes(query) ||
                            song.album.toLowerCase().includes(query)
                        );
                    }
                    
                    // 分类过滤
                    switch (this.currentFilter) {
                        case 'favorites':
                            filtered = filtered.filter(song => song.isFavorite);
                            break;
                        case 'recent':
                            filtered = filtered.sort((a, b) => new Date(b.dateAdded) - new Date(a.dateAdded));
                            break;
                        case 'mostPlayed':
                            filtered = filtered.sort((a, b) => b.playCount - a.playCount);
                            break;
                    }
                    
                    // 排序
                    if (this.currentFilter !== 'recent' && this.currentFilter !== 'mostPlayed') {
                        filtered = filtered.sort((a, b) => {
                            switch (this.sortBy) {
                                case 'title':
                                    return a.title.localeCompare(b.title);
                                case 'artist':
                                    return a.artist.localeCompare(b.artist);
                                case 'album':
                                    return a.album.localeCompare(b.album);
                                case 'duration':
                                    return a.duration - b.duration;
                                case 'dateAdded':
                                    return new Date(b.dateAdded) - new Date(a.dateAdded);
                                default:
                                    return 0;
                            }
                        });
                    }
                    
                    return filtered;
                },
                
                playSong(song) {
                    this.currentSong = song;
                    this.isPlaying = true;
                    song.playCount++;
                    console.log('🎵 播放歌曲:', song.title);
                },
                
                playAlbum(album) {
                    const albumSongs = this.songs.filter(song => song.album === album.name);
                    if (albumSongs.length > 0) {
                        this.playSong(albumSongs[0]);
                    }
                },
                
                togglePlay() {
                    this.isPlaying = !this.isPlaying;
                    console.log(this.isPlaying ? '▶️ 播放' : '⏸️ 暂停');
                },
                
                toggleFavorite(song) {
                    song.isFavorite = !song.isFavorite;
                    console.log(song.isFavorite ? '⚓ 已收藏' : '○ 取消收藏', song.title);
                },
                
                setFilter(filter) {
                    this.currentFilter = filter;
                    console.log('🔍 筛选:', filter);
                },
                
                setSortBy(sortBy) {
                    this.sortBy = sortBy;
                    this.showSortMenu = false;
                    console.log('📊 排序:', sortBy);
                },
                
                formatTime(seconds) {
                    const mins = Math.floor(seconds / 60);
                    const secs = Math.floor(seconds % 60);
                    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                },
                
                goBack() {
                    window.history.back();
                },
                
                openPlayer() {
                    window.location.href = './player.html';
                },
                
                init() {
                    console.log('🌊 歌曲列表页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 歌曲列表');
        });
    </script>
</body>
</html>
