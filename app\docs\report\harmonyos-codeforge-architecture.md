# HarmonyOS代码工坊 架构分析文档

## 介绍

本文档记录了HarmonyOS代码工坊的**当前状态**，包括技术债务、实际实现模式和现实约束条件。它为AI代理在此项目上工作提供参考。

### 文档范围

全面记录整个系统的架构、技术栈、模块组织和实际实现模式。

### 变更日志

| 日期 | 版本 | 描述 | 作者 |
|------|---------|-----------|--------|
| 2025-01-19 | 1.0 | 初始架构分析 | Winston架构师 |

## 快速参考 - 关键文件和入口点

### 理解系统的关键文件

- **主入口**: `products/phone/src/main/ets/page/MainPage.ets` (手机端主页面)
- **穿戴设备入口**: `products/wearable/src/main/ets/pages/MainPage.ets`
- **构建配置**: `build-profile.json5` (全局构建配置)
- **应用配置**: `AppScope/app.json5`
- **路由管理**: `common/src/main/ets/routermanager/PageContext.ets`
- **Sample下载脚本**: `hmosword-build/index.js`
- **网络配置**: `common/src/main/resources/rawfile/hmos_web_config.json`

### 核心业务逻辑

- **组件库**: `features/componentlibrary/` - ArkUI组件展示和代码生成
- **开发实践**: `features/devpractices/` - Sample代码管理和下载
- **探索模块**: `features/exploration/` - 文章和Banner内容
- **个人中心**: `features/mine/` - 用户相关功能
- **公共业务**: `features/commonbusiness/` - 跨模块共享业务逻辑
- **基础能力**: `common/` - 底层公共能力和工具

## 高层架构

### 技术概要

HarmonyOS代码工坊是一个基于ArkTS的原生HarmonyOS应用，采用严格的三层分层架构：
- **Products层**: 设备特定的入口和UI适配
- **Features层**: 业务特性模块，编译为HAR包
- **Common层**: 公共能力和基础服务

### 实际技术栈

| 类别 | 技术 | 版本 | 备注 |
|----------|------------|---------|--------|
| 运行时 | HarmonyOS | 5.0.2(14) | 兼容SDK版本 |
| 开发语言 | ArkTS | - | TypeScript超集 |
| UI框架 | ArkUI | - | 声明式UI框架 |
| 构建工具 | Hvigor | 5.0.0 | 官方构建系统 |
| 包管理 | oh-package | 5.0.0 | HarmonyOS包管理器 |
| 测试框架 | Hypium | 1.0.19 | 单元测试 |
| Mock框架 | Hamock | 1.0.0 | 测试Mock |

### 仓库结构现状

- **类型**: 单仓库多模块架构
- **包管理器**: oh-package (HarmonyOS官方)
- **特殊点**: 集成动态Sample下载机制

## 源码树和模块组织

### 项目结构（实际）

```text
sample_in_harmonyos/
├── AppScope/                    # 应用全局配置
│   ├── app.json5               # 应用基本信息
│   └── resources/              # 全局资源
├── common/                     # 公共能力层 (HAR包)
│   ├── src/main/ets/
│   │   ├── component/          # 公共UI组件
│   │   ├── routermanager/      # 路由管理
│   │   ├── storagemanager/     # 存储管理
│   │   ├── util/               # 工具类
│   │   └── viewmodel/          # ViewModel基类
│   └── build-profile.json5     # 构建配置
├── features/                   # 特性层
│   ├── commonbusiness/         # 公共业务 (HAR包)
│   ├── componentlibrary/       # 组件库 (HAR包)
│   ├── devpractices/          # 开发实践 (HAR包)
│   ├── exploration/           # 探索模块 (HAR包)
│   └── mine/                  # 个人中心 (HAR包)
├── products/                  # 产品层
│   ├── phone/                 # 手机端 (HAP包)
│   │   ├── src/main/
│   │   │   ├── ets/page/      # 页面入口
│   │   │   └── resources/     # 设备特定资源
│   │   └── build-profile.json5
│   └── wearable/              # 穿戴设备 (HAP包)
├── hmosword-build/            # Sample下载脚本
│   ├── index.js              # Node.js下载脚本
│   └── config/               # 配置文件
├── build-profile.json5        # 全局构建配置
├── hvigorfile.ts             # 构建入口
└── oh-package.json5          # 包依赖配置
```

### 关键模块及其用途

- **路由管理**: `common/src/main/ets/routermanager/` - 基于NavPathStack的导航
- **存储管理**: `common/src/main/ets/storagemanager/` - 数据持久化
- **组件库**: `features/componentlibrary/` - ArkUI组件展示和代码生成
- **Sample管理**: `features/devpractices/` - 动态Sample下载和安装
- **内容管理**: `features/exploration/` - 文章和Banner内容展示

## 数据模型和API

### 数据模型

核心数据模型位于各模块的viewmodel目录：
- **GlobalInfoModel**: 全局状态管理 - `common/src/main/ets/viewmodel/`
- **SampleDetailState**: Sample详情状态 - `features/devpractices/src/main/ets/viewmodel/`
- **ComponentDetailState**: 组件详情状态 - `features/componentlibrary/src/main/ets/viewmodel/`

### API规范

- **路由API**: 基于NavPathStack的声明式路由
- **存储API**: PreferenceManager封装的键值存储
- **网络API**: 基于Web组件的混合内容加载
- **Sample API**: moduleInstallManager动态模块安装

## 技术债务和已知问题

### 关键技术债务

1. **Sample下载机制**:
   - 位置: `hmosword-build/index.js`
   - 问题: Node.js脚本手动执行，未集成到构建流程
   - 影响: 开发者需要手动下载Sample才能体验完整功能

2. **混合内容架构**:
   - 位置: `products/phone/src/main/resources/resfile/`
   - 问题: HTML/JS/CSS混合在ArkTS项目中
   - 影响: 维护复杂度高，调试困难

3. **设备适配不一致**:
   - 位置: 各模块的module.json5
   - 问题: 设备类型配置不统一
   - 影响: 部分功能在某些设备上不可用

### 实际约束和限制

- **网络白名单**: 严格的URL白名单机制 (`hmos_web_config.json`)
- **动态模块限制**: Sample必须预先配置在应用市场
- **构建内存限制**: Hvigor配置了严格的内存使用限制
- **混淆配置**: 不同模块有不同的混淆策略

## 集成点和外部依赖

### 外部服务

| 服务 | 用途 | 集成类型 | 关键文件 |
|---------|---------|------------------|-----------|
| Gitee | Sample源码托管 | Git Clone | `hmosword-build/index.js` |
| 华为开发者网站 | 文档链接 | WebView | `articleUrlConfig.json` |
| 应用市场 | 动态模块分发 | moduleInstallManager | `SampleDetailPageVM.ets` |
| 华为账号 | 用户认证 | 待实现 | - |

### 内部集成点

- **模块间通信**: 基于@ohos包依赖的静态引用
- **状态管理**: AppStorage全局状态共享
- **路由导航**: NavPathStack统一路由管理
- **资源共享**: 分层资源引用机制

## 开发和部署

### 本地开发设置

1. **环境要求**:
   - DevEco Studio 5.0+
   - HarmonyOS SDK 5.0.2(14)
   - Node.js (用于Sample下载)
   - Git (用于Sample克隆)

2. **实际设置步骤**:
   ```bash
   # 1. 打开项目
   # 2. 等待Sync完成
   # 3. 配置签名证书
   # 4. 选择运行目标 (phone/wearable)
   # 5. 可选: 执行Sample下载
   cd hmosword-build && npm i && node index.js
   ```

### 构建和部署过程

- **构建命令**: Hvigor自动构建
- **部署方式**:
  - 开发: 直接安装到设备
  - 生产: 上传到华为应用市场
- **环境**: 开发环境、生产环境

### 设备支持策略

- **手机/平板**: 共包方案，通过一多能力适配
- **PC**: 分包方案，独立HAP包
- **穿戴设备**: 独立产品线，简化功能集

## 测试现状

### 当前测试覆盖

- **单元测试**: 基础框架已配置，实际覆盖率待评估
- **集成测试**: 主要依赖手动测试
- **端到端测试**: 无自动化测试
- **设备测试**: 手动在多设备上验证

### 运行测试

```bash
# 单元测试 (通过DevEco Studio)
# 集成测试 (手动执行)
# 设备测试 (连接真机/模拟器)
```

## 架构决策记录

### 关键架构决策

1. **三层分层架构**:
   - 决策: 采用Products/Features/Common三层架构
   - 原因: 支持多设备、模块化、可维护性
   - 约束: 严格的依赖方向，不允许反向依赖

2. **HAR/HAP包策略**:
   - 决策: Features编译为HAR包，Products编译为HAP包
   - 原因: 代码复用、独立部署、包大小优化
   - 约束: 动态模块需要应用市场支持

3. **混合内容架构**:
   - 决策: 文档内容使用HTML/CSS/JS
   - 原因: 内容丰富、样式灵活、快速迭代
   - 约束: 维护复杂、调试困难

## 附录 - 常用命令和脚本

### 常用开发命令

```bash
# 构建项目
hvigor assembleHap

# 清理构建
hvigor clean

# 下载Sample
cd hmosword-build && node index.js

# 安装依赖
npm i  # 在hmosword-build目录
```

### 调试和故障排除

- **日志**: 使用Logger工具类统一日志输出
- **调试模式**: DevEco Studio调试器
- **常见问题**:
  - Sample下载失败: 检查网络和Git配置
  - 构建内存不足: 调整hvigor-config.json5内存配置
  - 设备兼容性: 检查module.json5设备类型配置
