@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&family=Noto+Serif+SC:wght@400;700&display=swap');

/* 
 * 「听汀」全局样式系统 v1.0
 * 设计语言：中国风 Neo-Chinese × 极简主义 × 暖色低对比暗色
 * 核心概念：「水墨汀岸」- 水边静谧，音乐停泊
 */

/* ========== CSS 变量定义 ========== */
:root {
  /* 色彩体系 - 汀色五调 */
  
  /* 晨汀 (日间) 主题 */
  --color-morning-bg-primary: #FEFEFE;      /* 宣纸白 */
  --color-morning-bg-secondary: #F5F7FA;    /* 晨雾灰 */
  --color-morning-primary: #4A90E2;         /* 水波青 */
  --color-morning-accent: #D4AF37;          /* 锚点金 */
  --color-morning-text: #2C2C2C;            /* 墨黑 */
  --color-morning-text-secondary: #6B7280;  /* 淡墨 */
  --color-morning-border: #E5E5E5;          /* 淡墨分隔 */
  --color-morning-card: #FFFFFF;            /* 卡片白 */
  --color-morning-shadow: rgba(0, 0, 0, 0.08); /* 水波阴影 */
  
  /* 夜汀 (夜间) 主题 */
  --color-night-bg-primary: #0A0A0A;        /* 墨韵黑 */
  --color-night-bg-secondary: #1A1D23;      /* 深夜灰 */
  --color-night-primary: #2D4A6B;           /* 月光蓝 */
  --color-night-accent: #B8860B;            /* 古铜金 */
  --color-night-text: #E8E8E8;              /* 银白 */
  --color-night-text-secondary: #9CA3AF;    /* 月影灰 */
  --color-night-border: #2A2A2A;            /* 重墨分隔 */
  --color-night-card: #1a1d23;              /* 卡片深灰 */
  --color-night-shadow: rgba(0, 0, 0, 0.25); /* 深夜阴影 */
  
  /* 状态色彩 */
  --color-success: #48BB78;                 /* 水草绿 */
  --color-warning: #ED8936;                 /* 夕照橙 */
  --color-error: #F56565;                   /* 落日红 */
  --color-info: #4299E1;                    /* 水波蓝 */
  
  /* 字体系统 - 书韵现代 */
  --font-family-primary: Noto Serif SC, serif;
  --font-family-secondary: 'SF Pro Display', 'Roboto', system-ui, sans-serif;
  --font-family-mono: 'SF Mono', 'Roboto Mono', 'Consolas', monospace;
  
  /* 字体大小层级 */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  
  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-heavy: 900;
  
  /* 间距系统 */
  --spacing-xs: 0.25rem;       /* 4px */
  --spacing-sm: 0.5rem;        /* 8px */
  --spacing-md: 1rem;          /* 16px */
  --spacing-lg: 1.5rem;        /* 24px */
  --spacing-xl: 2rem;          /* 32px */
  --spacing-2xl: 3rem;         /* 48px */
  --spacing-3xl: 4rem;         /* 64px */
  
  /* 圆角系统 */
  --radius-sm: 0.25rem;        /* 4px */
  --radius-md: 0.5rem;         /* 8px */
  --radius-lg: 1rem;           /* 16px */
  --radius-xl: 1.5rem;         /* 24px */
  --radius-full: 9999px;       /* 圆形 */
  
  /* 阴影系统 - 水波效果 */
  --shadow-sm: 0 1px 2px var(--color-morning-shadow);
  --shadow-md: 0 4px 6px -1px var(--color-morning-shadow), 0 2px 4px -1px var(--color-morning-shadow);
  --shadow-lg: 0 10px 15px -3px var(--color-morning-shadow), 0 4px 6px -2px var(--color-morning-shadow);
  --shadow-xl: 0 20px 25px -5px var(--color-morning-shadow), 0 10px 10px -5px var(--color-morning-shadow);
  
  /* 过渡动画 */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 300ms ease-in-out;
  --transition-slow: 500ms ease-in-out;
  --transition-theme: 1200ms cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Z-index 层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

/* ========== 主题切换 ========== */
/* 晨汀主题 (默认) */
.theme-morning {
  --bg-primary: var(--color-morning-bg-primary);
  --bg-secondary: var(--color-morning-bg-secondary);
  --color-primary: var(--color-morning-primary);
  --color-accent: var(--color-morning-accent);
  --text-primary: var(--color-morning-text);
  --text-secondary: var(--color-morning-text-secondary);
  --border-color: var(--color-morning-border);
  --card-bg: var(--color-morning-card);
  --shadow-color: var(--color-morning-shadow);
}

/* 夜汀主题 */
.theme-night {
  --bg-primary: var(--color-night-bg-primary);
  --bg-secondary: var(--color-night-bg-secondary);
  --color-primary: var(--color-night-primary);
  --color-accent: var(--color-night-accent);
  --text-primary: var(--color-night-text);
  --text-secondary: var(--color-night-text-secondary);
  --border-color: var(--color-night-border);
  --card-bg: var(--color-night-card);
  --shadow-color: var(--color-night-shadow);
  
  /* 夜间模式阴影调整 */
  --shadow-sm: 0 1px 2px var(--color-night-shadow);
  --shadow-md: 0 4px 6px -1px var(--color-night-shadow), 0 2px 4px -1px var(--color-night-shadow);
  --shadow-lg: 0 10px 15px -3px var(--color-night-shadow), 0 4px 6px -2px var(--color-night-shadow);
  --shadow-xl: 0 20px 25px -5px var(--color-night-shadow), 0 10px 10px -5px var(--color-night-shadow);
}

/* ========== 基础样式重置 ========== */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  line-height: 1.6;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  font-family: var(--font-family-primary) !important;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color var(--transition-theme), color var(--transition-theme);
  overflow-x: hidden;
}

/* ========== 通用组件样式 ========== */

/* 卡片组件 */
.card {
  background-color: var(--card-bg);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--border-color);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-2px);
}

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  min-height: 44px; /* 无障碍设计 */
  font-size: var(--font-size-base);
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
}

.btn-primary:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn-accent {
  background-color: var(--color-accent);
  color: white;
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-ghost:hover {
  background-color: var(--bg-secondary);
}

/* 图标样式 */
.icon {
  width: 24px;
  height: 24px;
  display: inline-block;
  vertical-align: middle;
}

.icon-sm {
  width: 16px;
  height: 16px;
}

.icon-lg {
  width: 32px;
  height: 32px;
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-accent {
  color: var(--color-accent);
}

/* 标题层级 */
.title-xl {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-heavy);
  line-height: 1.2;
}

.title-lg {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  line-height: 1.3;
}

.title-md {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  line-height: 1.4;
}

/* 水波纹效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: var(--color-primary);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
  opacity: 0.3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
  
  .btn {
    min-height: 48px; /* 移动端更大的触摸目标 */
  }
}

@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
