# 「听汀」高保真原型

> 水边的静谧，音乐的停泊

一款专注于纯粹本地音乐播放的美学产品高保真原型，采用中国风 Neo-Chinese × 极简主义 × 暖色低对比暗色的设计语言。

## 🎯 项目概述

「听汀」是一个离线音乐播放器的高保真原型，体现了"船靠汀、人靠岸"的静谧美学。本项目使用现代Web技术栈实现了完整的用户界面和交互体验。

### 核心理念
- **纯粹性**：无广告、无推送、无网络依赖
- **美学性**：东方美学与现代设计的完美融合
- **情感性**：音乐收藏的情感化表达

### 项目特色 v3.1
- **完整生态系统**：21个页面涵盖音乐播放器完整生态
- **中国风设计语言**：融合传统美学与现代设计
- **毛玻璃质感**：Glass Morphism 设计风格
- **情感化交互**：锚点隐喻、水波反馈、温度感知
- **系统级功能**：用户管理、数据备份、隐私保护、版本更新
- **响应式适配**：完美支持多种设备尺寸
- **无障碍设计**：遵循 WCAG 2.1 标准
- **轻量级架构**：纯前端技术栈，快速加载
- **高保真还原**：接近真实应用的交互体验
- **技术优化**：Chart.js主题一致性，完善的页面导航

## 🚀 快速开始

### 环境要求
- 现代浏览器 (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- 本地Web服务器 (推荐使用Live Server)

### 安装运行
```bash
# 克隆项目
git clone [repository-url]
cd prototype

# 使用Live Server运行 (推荐)
# 或者使用Python简单服务器
python -m http.server 8000

# 访问页面
http://localhost:8000/pages/empty-state.html
```

### 页面导航

#### 核心页面 (v1.0)
- **空汀状态**：`pages/empty-state.html` - 首次使用引导页面
- **主界面**：`pages/main.html` - 音乐库主界面 (含快捷导航)
- **播放器**：`pages/player.html` - 全屏播放界面
- **歌曲列表**：`pages/song-list.html` - 歌曲浏览页面
- **设置**：`pages/settings.html` - 应用设置页面

#### 扩展页面 (v2.0)
- **搜索**：`pages/search.html` - 全局音乐搜索页面
- **个人中心**：`pages/profile.html` - 用户信息和统计页面
- **音乐库**：`pages/library.html` - 音乐分类管理页面
- **专辑详情**：`pages/album.html` - 专辑信息和歌曲列表
- **我的锚点**：`pages/anchors.html` - 收藏管理和情感记录
- **均衡器**：`pages/equalizer.html` - 音频均衡和音效设置

#### 测试页面
- **视觉测试**：`pages/visual-test.html` - 组件视觉一致性测试页面

## 🎨 设计系统

### 视觉语言
- **设计理念**：中国风 Neo-Chinese × 极简主义 × 毛玻璃美学
- **色彩系统**：水波青、锚点金、汀边白的温暖色调
- **字体系统**：苹方/PingFang SC 为主的中文字体栈
- **图标系统**：Material Symbols 配合中国风元素

### 交互原则
- **锚点隐喻**：收藏功能使用船锚图标，寓意情感的停泊
- **水波反馈**：点击和交互产生水波纹效果
- **温度感知**：界面色调随时间和使用习惯变化
- **诗意命名**：功能和页面采用富有诗意的中文命名

## 🎨 设计系统

### 色彩体系 - 「汀色五调」
```css
/* 晨汀主题 */
--color-morning-bg-primary: #FEFEFE;    /* 宣纸白 */
--color-morning-primary: #4A90E2;       /* 水波青 */
--color-morning-accent: #D4AF37;        /* 锚点金 */

/* 夜汀主题 */
--color-night-bg-primary: #0A0A0A;      /* 墨韵黑 */
--color-night-primary: #2D4A6B;         /* 月光蓝 */
--color-night-accent: #B8860B;          /* 古铜金 */
```

### 字体系统 - 「书韵现代」
- **中文**：思源黑体系列 (Heavy/Bold/Regular/Light)
- **英文**：SF Pro Display / Roboto
- **等宽**：SF Mono / Roboto Mono

### 材质系统 - 「水之质感」
- 水面玻璃：毛玻璃效果 + 水波纹
- 宣纸纹理：微妙的纸张质感
- 金属锚点：拉丝金属质感
- 水墨晕染：动态渐变效果

## 🛠️ 技术架构

### 前端技术栈
- **HTML5**：语义化标记
- **Tailwind CSS 3.4.0**：原子化CSS框架
- **Alpine.js 3.13.0**：轻量级响应式框架
- **Iconify Icons 2.0.0**：统一图标系统
- **Chart.js 4.4.0**：数据可视化（预留）

### 核心特性
- ✅ **双主题系统**：晨汀/夜汀主题无缝切换
- ✅ **响应式设计**：支持手机、平板、桌面多端适配
- ✅ **交互动效**：水波纹、锚点动画等精致交互
- ✅ **无障碍设计**：键盘导航、屏幕阅读器支持
- ✅ **iOS规范**：严格遵循iOS/iPad设计规范
- ✅ **毛玻璃美学**：Glass Morphism设计语言
- ✅ **视觉一致性**：统一的组件样式和交互模式

## 📱 响应式支持

### 断点系统
- **小屏手机**：< 375px (iPhone SE)
- **标准手机**：375px - 767px (iPhone 12/13)
- **小平板**：768px - 1023px (iPad mini)
- **大平板**：1024px - 1365px (iPad Pro)
- **桌面**：≥ 1366px

### 布局适配
- **手机端**：单列布局，底部控制栏
- **平板端**：双列布局，侧边播放详情
- **桌面端**：三列布局，完整功能展示

## 🎭 交互动效

### 核心动画
- **水波纹效果**：点击反馈，0.6s径向扩散
- **锚点动画**：收藏操作，0.8s下落+水波
- **主题切换**：1.2s径向渐变过渡
- **卡片悬停**：0.3s缓动变换

### 动画控制
```javascript
// 创建水波纹效果
AnimationUtils.createRipple(element, event, color);

// 锚点下落动画
AnimationUtils.anchorDropAnimation(element);

// 主题切换动画
AnimationUtils.themeTransitionAnimation(newTheme);
```

## 🎵 功能模块

### 1. 空汀状态 (`empty-state.html`)
- 水墨码头插画
- 浮动粒子效果
- 扫描音乐引导
- 主题切换

### 2. 主界面 (`main.html`)
- 当前播放卡片
- 标签式导航
- 歌曲列表视图
- 底部播放控制

### 3. 播放器 (`player.html`)
- 旋转封面动画
- 滚动歌词显示
- 水波进度条
- 全屏播放控制

### 4. 歌曲列表 (`song-list.html`)
- 列表/网格视图切换
- 搜索和筛选
- 排序功能
- 播放状态指示

### 5. 设置页面 (`settings.html`)
- 主题预览切换
- 音质设置
- 系统配置
- 关于信息

## 🔧 开发指南

### 文件结构
```
prototype/
├── assets/
│   ├── css/
│   │   ├── styles.css          # 全局样式系统
│   │   ├── responsive.css      # 响应式设计
│   │   └── visual-guidelines.css # 视觉设计规范
│   └── js/
│       ├── theme.js           # 主题管理系统
│       ├── animations.js      # 交互动效系统
│       └── tailwind.config.js # Tailwind配置
├── pages/
│   ├── empty-state.html       # 空汀状态页面
│   ├── main.html             # 主界面页面
│   ├── player.html           # 播放界面页面
│   ├── song-list.html        # 歌曲列表页面
│   ├── settings.html         # 设置页面
│   └── visual-test.html      # 视觉测试页面
├── PROJECT_SUMMARY.md         # 项目总结
├── DEVELOPMENT_PROGRESS.md    # 开发进度
└── README.md                 # 项目说明
```

### 主题切换使用
```html
<!-- 主题切换按钮 -->
<button data-theme-toggle>
  <span class="theme-icon">☀️</span>
  <span class="theme-text">晨汀</span>
</button>
```

### 动画效果使用
```html
<!-- 水波纹效果 -->
<button x-ripple="'var(--color-primary)'">点击我</button>

<!-- 锚点动画 -->
<button x-anchor-drop>收藏</button>

<!-- 卡片悬停 -->
<div x-card-hover>卡片内容</div>
```

## 🌐 浏览器兼容性

| 浏览器 | 版本要求 | 支持状态 |
|--------|----------|----------|
| Chrome | 90+ | ✅ 完全支持 |
| Firefox | 88+ | ✅ 完全支持 |
| Safari | 14+ | ✅ 完全支持 |
| Edge | 90+ | ✅ 完全支持 |
| iOS Safari | 14+ | ✅ 完全支持 |
| Chrome Mobile | 90+ | ✅ 完全支持 |

## ♿ 无障碍支持

- ✅ **键盘导航**：完整的Tab键导航支持
- ✅ **屏幕阅读器**：语义化标签和ARIA属性
- ✅ **高对比度**：支持系统高对比度模式
- ✅ **减少动画**：尊重用户的动画偏好设置
- ✅ **大字体**：支持系统字体缩放
- ✅ **触摸目标**：最小44×44pt触摸区域

## 📊 性能优化

### 加载性能
- CDN资源并行加载
- 关键CSS内联
- 图标按需加载

### 运行性能
- CSS动画优于JavaScript
- GPU加速transform
- 减少重排重绘

### 内存优化
- 事件监听器清理
- DOM节点复用
- 图片懒加载预留

## 🔮 后续规划

### 短期目标 (1-2周)
- [ ] 添加音频播放引擎
- [ ] 实现本地文件访问
- [ ] 完善错误处理

### 中期目标 (1-2月)
- [ ] PWA支持
- [ ] 离线缓存
- [ ] 手势识别

### 长期目标 (3-6月)
- [ ] 组件库提取
- [ ] 主题编辑器
- [ ] 多语言支持

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 👥 开发团队

- **James** - Full Stack Developer - 原型开发与技术实现
- **Alex** - UX Designer - 设计系统与用户体验
- **Sarah** - Product Manager - 产品需求与项目管理

## 🙏 致谢

感谢以下开源项目的支持：
- [Tailwind CSS](https://tailwindcss.com/) - 原子化CSS框架
- [Alpine.js](https://alpinejs.dev/) - 轻量级响应式框架
- [Iconify](https://iconify.design/) - 统一图标系统
- [Chart.js](https://www.chartjs.org/) - 数据可视化库

---

**「听汀」** - 让音乐在心中停泊 🎵⚓

*Built with ❤️ by the TingTing Team*
