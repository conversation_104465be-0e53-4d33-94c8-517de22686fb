*agent ux-expert

您好！我是UX设计师Alex。刚刚看到了Sarah的PRD，这个"听汀"的概念让我非常兴奋！让我为您创建完整的UI/UX设计规范，将"船靠汀、人靠岸"的静谧美学转化为具体的设计语言。

*create-doc ui-ux-spec

正在创建UI/UX设计规范...

---

# 「听汀」UI/UX设计规范文档 v1.0

## 1. 设计哲学与品牌语言

### 1.1 核心设计理念
```
"水边的静谧，音乐的停泊"
- 极简主义 × 东方美学
- 功能性 × 情感化
- 数字静修 × 音乐收藏
```

### 1.2 品牌关键词
- **静谧** (Serenity) - 无干扰的纯净体验
- **停泊** (Anchoring) - 情感的归属感
- **水波** (Ripple) - 流动的优雅
- **收藏** (Collection) - 珍视的仪式感

### 1.3 设计原则
1. **减法设计**：去除一切非必要元素
2. **情感留白**：给用户想象和感受的空间
3. **触觉反馈**：每一次交互都有温度
4. **时间感知**：界面随时间自然变化

## 2. 视觉设计系统

### 2.1 色彩体系

#### 2.1.1 主色调 - "汀水蓝"
```
晨汀 (Morning Mist)
- 主色：#E8F4F8 (晨雾蓝)
- 辅色：#B8D4E3 (晨波蓝)
- 点缀：#7A9CC6 (晨影蓝)

夜汀 (Night Harbor)
- 主色：#0F1419 (深夜蓝)
- 辅色：#1E293B (夜波蓝)
- 点缀：#4A5568 (月影蓝)
```

#### 2.1.2 中性色 - "沙洲灰"
```
- 背景：#FFFFFF / #0F1419
- 卡片：#F7FAFC / #1A202C
- 分割线：#E2E8F0 / #2D3748
- 文字：#1A202C / #F7FAFC
```

#### 2.1.3 状态色
```
- 收藏（锚点）：#D4AF37 (古铜锚)
- 播放：#4299E1 (水波蓝)
- 成功：#48BB78 (水草绿)
- 警告：#ED8936 (夕照橙)
```

### 2.2 字体系统

#### 2.2.1 英文字体
```
标题：SF Pro Display / Roboto
正文：SF Pro Text / Roboto
等宽：SF Mono / Roboto Mono
```

#### 2.2.2 中文字体
```
标题：苹方-中黑体 / 思源黑体
正文：苹方-常规体 / 思源黑体
```

#### 2.2.3 字体层级
```
大标题：24pt / 32pt
标题：20pt / 24pt
正文：16pt / 18pt
小字：14pt / 16pt
最小：12pt / 14pt
```

### 2.3 图标系统

#### 2.3.1 图标风格
- **线性图标**：简洁、现代
- **圆角处理**：友好、温和
- **统一线宽**：2px
- **网格系统**：24×24px

#### 2.3.2 核心图标
```
播放：▶️ 圆角三角形
暂停：⏸️ 双竖线
收藏：⚓ 锚点图标
列表：≡ 水波纹
设置：⚙️ 简化齿轮
```

### 2.4 插画与装饰

#### 2.4.1 空状态插画
- **空汀画面**：简洁码头轮廓
- **无搜索结果**：平静水面
- **无网络**：锚定的船

#### 2.4.2 装饰元素
- **水波纹**：动态背景
- **光斑效果**：阳光在水面
- **渐变过渡**：晨昏变化

## 3. 界面设计详解

### 3.1 启动体验

#### 3.1.1 启动画面
```
场景：晨雾中的码头
动画：水波渐渐平静
文案：听汀 - 你的音乐港湾
时长：2秒
```

#### 3.1.2 首次引导
```
1. 欢迎文案："欢迎来到你的私人音乐汀岸"
2. 权限说明："我们需要访问音乐文件"
3. 扫描引导："请选择音乐文件夹"
4. 空汀展示："现在，让我们开始建造你的汀"
```

### 3.2 主界面设计

#### 3.2.1 布局结构
```
┌─────────────────────────────┐
│  状态栏                      │
├─────────────────────────────┤
│  标题栏：听汀               │
├─────────────────────────────┤
│  ┌───────────────────────┐  │
│  │  当前播放卡片         │  │
│  │  (封面+歌曲信息)      │  │
│  └───────────────────────┘  │
├─────────────────────────────┤
│  标签栏：                   │
│  [歌曲] [专辑] [艺术家]     │
├─────────────────────────────┤
│  内容区域：                  │
│  列表视图                   │
├─────────────────────────────┤
│  底部播放控制栏              │
└─────────────────────────────┘
```

#### 3.2.2 当前播放卡片
```
设计元素：
- 圆角卡片（16px）
- 阴影效果（水波阴影）
- 封面图片（圆形或圆角）
- 歌曲标题（大字号）
- 艺术家（小字号）
- 播放进度条（水波动画）
```

### 3.3 空汀状态设计

#### 3.3.1 空列表画面
```
视觉元素：
- 简洁码头轮廓（线稿）
- 平静水面（淡蓝色）
- 无船（留白）
- 柔和光线

文案：
主标题："空汀"
副标题："请把音乐放进你的汀"
按钮："扫描音乐"
```

#### 3.3.2 引导动画
```
1. 码头轮廓淡淡出现
2. 水面轻微波动
3. 文字渐现
4. 按钮微光效果
```

### 3.4 播放界面

#### 3.4.1 全屏播放
```
┌─────────────────────────────┐
│  返回按钮   分享    更多    │
├─────────────────────────────┤
│                             │
│  ┌───────────────────────┐  │
│  │      专辑封面         │  │
│  │   (圆形，大尺寸)     │  │
│  └───────────────────────┘  │
│                             │
│  歌曲标题                   │
│  艺术家                    │
│                             │
│  ┌───────────────────────┐  │
│  │    歌词区域           │  │
│  │  (可滑动，高亮当前)   │  │
│  └───────────────────────┘  │
│                             │
│  进度条                     │
│  播放控制                   │
│  [♡] [⏮️] [⏯️] [⏭️] [🔁]    │
└─────────────────────────────┘
```

#### 3.4.2 交互细节
```
- 封面旋转：播放时缓慢旋转
- 歌词高亮：当前行高亮显示
- 进度条：水波填充效果
- 收藏：锚点图标动画
```

### 3.5 列表视图设计

#### 3.5.1 歌曲列表
```
┌─────────────────────────────┐
│ ┌─────┐ 歌曲标题             │
│ │封面 │ 艺术家 - 专辑        │
│ └─────┘ 时长                │
│ ───────────────────────────  │
│ ┌─────┐ 歌曲标题             │
│ │封面 │ 艺术家 - 专辑        │
│ └─────┘ 时长                │
└─────────────────────────────┘
```

#### 3.5.2 悬停效果
- 背景色轻微变化（水波纹）
- 收藏图标淡入
- 长按出现操作菜单

### 3.6 主题切换

#### 3.6.1 晨汀主题
```
色彩：
- 背景：淡蓝色渐变
- 前景：深灰色文字
- 强调：金色锚点
- 水波：浅蓝色涟漪

氛围：清新、希望、开始
```

#### 3.6.2 夜汀主题
```
色彩：
- 背景：深蓝色渐变
- 前景：浅灰色文字
- 强调：月光银色
- 水波：深蓝色涟漪

氛围：宁静、深邃、思考
```

## 4. 交互设计

### 4.1 手势操作

#### 4.1.1 全局手势
```
- 左右滑动：切换标签页
- 上下滑动：列表滚动
- 双指缩放：调整字体大小
- 长按：显示上下文菜单
```

#### 4.1.2 播放界面手势
```
- 左右滑动：切换歌曲
- 上下滑动：调节音量
- 双击封面：收藏/取消
- 长按封面：显示详细信息
```

### 4.2 触觉反馈

#### 4.2.1 反馈场景
- 播放/暂停：轻微震动
- 收藏：中等震动（锚定感）
- 切换歌曲：滑动震动
- 错误操作：警示震动

### 4.3 动画设计

#### 4.3.1 过渡动画
```
- 页面切换：水波扩散
- 列表加载：淡入动画
- 播放控制：弹性动画
- 主题切换：渐变过渡
```

#### 4.3.2 微交互
- 按钮点击：缩放效果
- 收藏：锚点下落动画
- 进度条：水波填充
- 加载：旋转水纹

## 5. 响应式设计

### 5.1 断点设计
```
- 小屏手机：< 375px
- 标准手机：375px - 768px
- 大屏手机/小平板：768px - 1024px
- 平板：≥ 1024px
```

### 5.2 适配策略
- **手机**：单列布局
- **平板**：双列布局（左侧列表，右侧详情）
- **横屏**：特殊优化播放界面

## 6. 无障碍设计

### 6.1 视觉无障碍
- 字体大小调节（3级）
- 高对比度模式
- 色盲友好配色
- 屏幕阅读器支持

### 6.2 操作无障碍
- 大按钮设计（最小44×44pt）
- 语音控制支持
- 键盘导航支持
- 减少手势依赖

## 7. 原型与测试

### 7.1 低保真原型
- 手绘草图（已完成）
- 纸质原型测试
- 用户反馈收集

### 7.2 高保真原型
- Figma设计稿（提供链接）
- 交互原型演示
- 用户可用性测试

### 7.3 测试场景
```
场景1：首次使用
- 用户能否快速理解产品概念？
- 空汀状态是否足够引导？

场景2：日常播放
- 播放控制是否直观？
- 列表浏览是否流畅？

场景3：收藏管理
- 收藏操作是否明显？
- 播放列表管理是否易用？
```

## 8. 设计交付物

### 8.1 设计规范
- 完整设计稿（Figma）
- 组件库文档
- 交互原型
- 切图资源

### 8.2 开发交接
- 设计标注（间距、颜色、字体）
- 切图资源（2x, 3x）
- 动画规范（时长、缓动）
- 主题配置

---

**设计完成！**  
**UX设计师**：Alex  
**日期**：2025年7月14日  
**状态**：设计规范完成，准备进入开发阶段

接下来由架构师为您设计技术架构！