<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册账户 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .register-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .register-card {
            background: var(--card-bg);
            border-radius: 24px;
            padding: 3rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            width: 100%;
            max-width: 400px;
            position: relative;
            overflow: hidden;
        }
        
        .register-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
        }
        
        .form-input {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.1);
            transform: translateY(-2px);
        }
        
        .form-input.error {
            border-color: #ef4444;
            box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
        }
        
        .form-error {
            color: #ef4444;
            font-size: 0.75rem;
            margin-top: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .submit-button {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }
        
        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
        }
        
        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .social-login {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .social-button {
            flex: 1;
            padding: 0.875rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .social-button:hover {
            border-color: var(--color-primary);
            background: var(--card-bg);
            transform: translateY(-2px);
        }
        
        .checkbox-group {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }
        
        .custom-checkbox {
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-radius: 4px;
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 2px;
        }
        
        .custom-checkbox.checked {
            background: var(--color-primary);
            border-color: var(--color-primary);
        }
        
        .checkbox-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
            line-height: 1.5;
        }
        
        .checkbox-label a {
            color: var(--color-primary);
            text-decoration: none;
        }
        
        .checkbox-label a:hover {
            text-decoration: underline;
        }
        
        .login-link {
            text-align: center;
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
        
        .login-link a {
            color: var(--color-primary);
            text-decoration: none;
            font-weight: 600;
        }
        
        .login-link a:hover {
            text-decoration: underline;
        }
        
        .brand-logo {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .brand-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 0.5rem;
        }
        
        .brand-subtitle {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="register-container water-bg" x-data="registerApp()">
        <div class="register-card">
            <!-- 品牌Logo -->
            <div class="brand-logo">
                <h1 class="brand-title">听汀</h1>
                <p class="brand-subtitle">水边的静谧，音乐的停泊</p>
            </div>
            
            <!-- 注册表单 -->
            <form @submit.prevent="handleRegister()">
                <div class="form-group">
                    <label class="form-label">邮箱地址</label>
                    <input type="email" 
                           x-model="form.email" 
                           class="form-input"
                           :class="errors.email ? 'error' : ''"
                           placeholder="请输入邮箱地址"
                           required>
                    <div x-show="errors.email" class="form-error">
                        <iconify-icon icon="material-symbols:error" class="text-xs"></iconify-icon>
                        <span x-text="errors.email"></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">昵称</label>
                    <input type="text" 
                           x-model="form.nickname" 
                           class="form-input"
                           :class="errors.nickname ? 'error' : ''"
                           placeholder="请输入昵称"
                           required>
                    <div x-show="errors.nickname" class="form-error">
                        <iconify-icon icon="material-symbols:error" class="text-xs"></iconify-icon>
                        <span x-text="errors.nickname"></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" 
                           x-model="form.password" 
                           class="form-input"
                           :class="errors.password ? 'error' : ''"
                           placeholder="请输入密码"
                           required>
                    <div x-show="errors.password" class="form-error">
                        <iconify-icon icon="material-symbols:error" class="text-xs"></iconify-icon>
                        <span x-text="errors.password"></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="form-label">确认密码</label>
                    <input type="password" 
                           x-model="form.confirmPassword" 
                           class="form-input"
                           :class="errors.confirmPassword ? 'error' : ''"
                           placeholder="请再次输入密码"
                           required>
                    <div x-show="errors.confirmPassword" class="form-error">
                        <iconify-icon icon="material-symbols:error" class="text-xs"></iconify-icon>
                        <span x-text="errors.confirmPassword"></span>
                    </div>
                </div>
                
                <!-- 协议确认 -->
                <div class="checkbox-group">
                    <div class="custom-checkbox" 
                         :class="form.agreeTerms ? 'checked' : ''"
                         @click="form.agreeTerms = !form.agreeTerms">
                        <iconify-icon x-show="form.agreeTerms" icon="material-symbols:check" class="text-white text-sm"></iconify-icon>
                    </div>
                    <div class="checkbox-label">
                        我已阅读并同意
                        <a href="./user-agreement.html" target="_blank">《用户协议》</a>
                        和
                        <a href="./privacy-policy.html" target="_blank">《隐私政策》</a>
                    </div>
                </div>
                
                <!-- 注册按钮 -->
                <button type="submit" 
                        class="submit-button"
                        :disabled="!canSubmit"
                        x-text="isLoading ? '注册中...' : '创建账户'">
                </button>
            </form>
            
            <!-- 社交登录 -->
            <div class="social-login">
                <button @click="socialLogin('wechat')" class="social-button">
                    <iconify-icon icon="material-symbols:wechat" class="text-lg"></iconify-icon>
                    微信
                </button>
                <button @click="socialLogin('qq')" class="social-button">
                    <iconify-icon icon="material-symbols:account-circle" class="text-lg"></iconify-icon>
                    QQ
                </button>
            </div>
            
            <!-- 登录链接 -->
            <div class="login-link">
                已有账户？<a href="./login.html">立即登录</a>
            </div>
        </div>
    </div>
    
    <script>
        function registerApp() {
            return {
                isLoading: false,
                
                form: {
                    email: '',
                    nickname: '',
                    password: '',
                    confirmPassword: '',
                    agreeTerms: false
                },
                
                errors: {},
                
                get canSubmit() {
                    return this.form.email && 
                           this.form.nickname && 
                           this.form.password && 
                           this.form.confirmPassword && 
                           this.form.agreeTerms &&
                           !this.isLoading;
                },
                
                validateForm() {
                    this.errors = {};
                    
                    // 邮箱验证
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(this.form.email)) {
                        this.errors.email = '请输入有效的邮箱地址';
                    }
                    
                    // 昵称验证
                    if (this.form.nickname.length < 2) {
                        this.errors.nickname = '昵称至少需要2个字符';
                    }
                    
                    // 密码验证
                    if (this.form.password.length < 6) {
                        this.errors.password = '密码至少需要6个字符';
                    }
                    
                    // 确认密码验证
                    if (this.form.password !== this.form.confirmPassword) {
                        this.errors.confirmPassword = '两次输入的密码不一致';
                    }
                    
                    return Object.keys(this.errors).length === 0;
                },
                
                async handleRegister() {
                    if (!this.validateForm()) {
                        return;
                    }
                    
                    this.isLoading = true;
                    
                    try {
                        // 模拟注册请求
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        console.log('📝 注册成功', {
                            email: this.form.email,
                            nickname: this.form.nickname
                        });
                        
                        // 注册成功后跳转到主页面
                        window.location.href = './main.html';
                        
                    } catch (error) {
                        console.error('注册失败:', error);
                        this.errors.general = '注册失败，请稍后重试';
                    } finally {
                        this.isLoading = false;
                    }
                },
                
                socialLogin(platform) {
                    console.log('🔗 社交登录:', platform);
                    // 这里实现社交登录逻辑
                },
                
                init() {
                    console.log('📝 注册页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 用户注册');
        });
    </script>
</body>
</html>
