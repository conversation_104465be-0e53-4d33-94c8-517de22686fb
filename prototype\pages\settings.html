<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设置 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">

    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>

    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        /* 设置页面特殊样式 */
        .settings-container {
            background: linear-gradient(135deg, 
                var(--bg-primary) 0%, 
                var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .settings-card {
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            border: 1px solid var(--border-color);
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .settings-card:hover {
            box-shadow: var(--shadow-md);
        }
        
        .settings-section {
            border-bottom: 1px solid var(--border-color);
        }
        
        .settings-section:last-child {
            border-bottom: none;
        }
        
        .settings-item {
            padding: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid var(--border-color);
            transition: background-color 0.2s ease;
        }
        
        .settings-item:last-child {
            border-bottom: none;
        }
        
        .settings-item:hover {
            background-color: var(--bg-secondary);
        }
        
        .toggle-switch {
            position: relative;
            display: inline-block;
            width: 48px;
            height: 24px;
        }
        
        .toggle-switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }
        
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: var(--border-color);
            transition: .4s;
            border-radius: 24px;
        }
        
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 18px;
            width: 18px;
            left: 3px;
            bottom: 3px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        
        input:checked + .toggle-slider {
            background-color: var(--color-primary);
        }
        
        input:checked + .toggle-slider:before {
            transform: translateX(24px);
        }
        
        .radio-button {
            display: inline-block;
            position: relative;
            padding-left: 30px;
            cursor: pointer;
            user-select: none;
        }
        
        .radio-button input {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }
        
        .radio-mark {
            position: absolute;
            top: 0;
            left: 0;
            height: 20px;
            width: 20px;
            background-color: var(--bg-secondary);
            border: 2px solid var(--border-color);
            border-radius: 50%;
        }
        
        .radio-button:hover .radio-mark {
            background-color: var(--bg-primary);
        }
        
        .radio-button input:checked ~ .radio-mark {
            background-color: var(--color-primary);
            border-color: var(--color-primary);
        }
        
        .radio-mark:after {
            content: "";
            position: absolute;
            display: none;
        }
        
        .radio-button input:checked ~ .radio-mark:after {
            display: block;
        }
        
        .radio-button .radio-mark:after {
            top: 4px;
            left: 4px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: white;
        }
        
        .slider-container {
            width: 100%;
            display: flex;
            align-items: center;
        }
        
        .slider {
            -webkit-appearance: none;
            width: 100%;
            height: 4px;
            border-radius: 2px;
            background: var(--border-color);
            outline: none;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--color-primary);
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .slider::-moz-range-thumb {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: var(--color-primary);
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .theme-preview {
            width: 100px;
            height: 60px;
            border-radius: var(--radius-md);
            overflow: hidden;
            position: relative;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }
        
        .theme-preview.active {
            border-color: var(--color-accent);
            transform: scale(1.05);
        }
        
        .theme-preview-morning {
            background: linear-gradient(135deg, #FEFEFE 0%, #F5F7FA 100%);
        }
        
        .theme-preview-night {
            background: linear-gradient(135deg, #0A0A0A 0%, #1A1D23 100%);
        }
        
        .theme-preview-text {
            position: absolute;
            bottom: 5px;
            left: 5px;
            font-size: 10px;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .theme-preview-morning .theme-preview-text {
            background: #4A90E2;
            color: white;
        }
        
        .theme-preview-night .theme-preview-text {
            background: #2D4A6B;
            color: white;
        }
    </style>
</head>
<body class="font-primary">
    <!-- 主题管理器 -->
    <script src="../assets/js/theme.js"></script>
    
    <!-- 设置页面 -->
    <div class="settings-container water-bg" x-data="settingsApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">设置</h1>
                </div>

                <div class="header-actions">
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 设置内容 -->
        <main class="px-6 pb-32">
            <!-- 主题设置 -->
            <div class="settings-card mb-6">
                <div class="settings-section">
                    <div class="p-4 border-b border-border-color">
                        <h2 class="text-lg font-semibold text-primary">主题设置</h2>
                    </div>
                    
                    <div class="p-4">
                        <h3 class="text-sm font-medium text-secondary mb-4">选择主题</h3>
                        <div class="flex space-x-4">
                            <div @click="setTheme('theme-morning')" 
                                 :class="currentTheme === 'theme-morning' ? 'active' : ''" 
                                 class="theme-preview theme-preview-morning cursor-pointer">
                                <div class="theme-preview-text">晨汀</div>
                            </div>
                            <div @click="setTheme('theme-night')" 
                                 :class="currentTheme === 'theme-night' ? 'active' : ''" 
                                 class="theme-preview theme-preview-night cursor-pointer">
                                <div class="theme-preview-text">夜汀</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">跟随系统</h3>
                            <p class="text-xs text-secondary">根据系统深色模式自动切换</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" x-model="followSystem" @change="toggleFollowSystem()">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 播放设置 -->
            <div class="settings-card mb-6">
                <div class="settings-section">
                    <div class="p-4 border-b border-border-color">
                        <h2 class="text-lg font-semibold text-primary">播放设置</h2>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">音质设置</h3>
                            <p class="text-xs text-secondary">选择播放音质</p>
                        </div>
                        <div class="flex flex-col space-y-2">
                            <label class="radio-button text-sm">
                                标准音质
                                <input type="radio" name="quality" value="standard" x-model="audioQuality">
                                <span class="radio-mark"></span>
                            </label>
                            <label class="radio-button text-sm">
                                高品质
                                <input type="radio" name="quality" value="high" x-model="audioQuality">
                                <span class="radio-mark"></span>
                            </label>
                            <label class="radio-button text-sm">
                                无损音质
                                <input type="radio" name="quality" value="lossless" x-model="audioQuality">
                                <span class="radio-mark"></span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">均衡器</h3>
                            <p class="text-xs text-secondary">自定义音频均衡</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" x-model="equalizerEnabled">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <div class="settings-item" x-show="equalizerEnabled">
                        <div class="w-full">
                            <div class="flex justify-between mb-2">
                                <span class="text-sm text-secondary">低音</span>
                                <span class="text-sm text-secondary">高音</span>
                            </div>
                            <div class="slider-container">
                                <input type="range" min="0" max="100" x-model="equalizerValue" class="slider">
                            </div>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">智能增益控制</h3>
                            <p class="text-xs text-secondary">防止音量突变</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" x-model="gainControl">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                </div>
            </div>
            
            <!-- 扫描设置 -->
            <div class="settings-card mb-6">
                <div class="settings-section">
                    <div class="p-4 border-b border-border-color">
                        <h2 class="text-lg font-semibold text-primary">音乐库设置</h2>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">扫描目录</h3>
                            <p class="text-xs text-secondary">选择音乐文件夹</p>
                        </div>
                        <button class="px-3 py-1 rounded-lg bg-bg-secondary text-secondary text-sm hover:bg-primary hover:text-white transition-colors">
                            选择
                        </button>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">自动扫描</h3>
                            <p class="text-xs text-secondary">启动时自动扫描新音乐</p>
                        </div>
                        <label class="toggle-switch">
                            <input type="checkbox" x-model="autoScan">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">支持的格式</h3>
                            <p class="text-xs text-secondary">MP3, FLAC, WAV, M4A, OGG</p>
                        </div>
                        <button class="px-3 py-1 rounded-lg bg-bg-secondary text-secondary text-sm hover:bg-primary hover:text-white transition-colors">
                            编辑
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 页面导航 -->
            <div class="settings-card mb-6">
                <div class="settings-section">
                    <div class="p-4 border-b border-border-color">
                        <h2 class="text-lg font-semibold text-primary">页面导航</h2>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./search.html')">
                        <div>
                            <h3 class="text-primary">搜索页面</h3>
                            <p class="text-xs text-secondary">全局音乐搜索</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./profile.html')">
                        <div>
                            <h3 class="text-primary">个人中心</h3>
                            <p class="text-xs text-secondary">用户信息和统计</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./library.html')">
                        <div>
                            <h3 class="text-primary">音乐库</h3>
                            <p class="text-xs text-secondary">音乐分类管理</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./album.html')">
                        <div>
                            <h3 class="text-primary">专辑页面</h3>
                            <p class="text-xs text-secondary">专辑详情展示</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./artist.html')">
                        <div>
                            <h3 class="text-primary">艺术家页面</h3>
                            <p class="text-xs text-secondary">艺术家详情</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./song-detail.html')">
                        <div>
                            <h3 class="text-primary">歌曲详情</h3>
                            <p class="text-xs text-secondary">歌曲信息和歌词</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./playlist.html')">
                        <div>
                            <h3 class="text-primary">播放列表</h3>
                            <p class="text-xs text-secondary">播放列表管理</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./anchors.html')">
                        <div>
                            <h3 class="text-primary">我的锚点</h3>
                            <p class="text-xs text-secondary">收藏和情感记录</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./music-space.html')">
                        <div>
                            <h3 class="text-primary">音乐空间</h3>
                            <p class="text-xs text-secondary">个性化音乐展示</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./equalizer.html')">
                        <div>
                            <h3 class="text-primary">均衡器</h3>
                            <p class="text-xs text-secondary">音频均衡设置</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./desktop-widget.html')">
                        <div>
                            <h3 class="text-primary">桌面小组件</h3>
                            <p class="text-xs text-secondary">桌面音乐控制</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./backup.html')">
                        <div>
                            <h3 class="text-primary">数据备份</h3>
                            <p class="text-xs text-secondary">备份恢复数据</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./privacy-settings.html')">
                        <div>
                            <h3 class="text-primary">隐私设置</h3>
                            <p class="text-xs text-secondary">隐私保护控制</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./help.html')">
                        <div>
                            <h3 class="text-primary">帮助与反馈</h3>
                            <p class="text-xs text-secondary">获取帮助和反馈</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./update.html')">
                        <div>
                            <h3 class="text-primary">检查更新</h3>
                            <p class="text-xs text-secondary">版本更新管理</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./contact.html')">
                        <div>
                            <h3 class="text-primary">联系我们</h3>
                            <p class="text-xs text-secondary">客服支持联系</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./features.html')">
                        <div>
                            <h3 class="text-primary">功能介绍</h3>
                            <p class="text-xs text-secondary">应用功能展示</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>

                    <div class="settings-item cursor-pointer" @click="navigateTo('./onboarding.html')">
                        <div>
                            <h3 class="text-primary">新手引导</h3>
                            <p class="text-xs text-secondary">重新查看引导</p>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>
                </div>
            </div>

            <!-- 关于 -->
            <div class="settings-card mb-6">
                <div class="settings-section">
                    <div class="p-4 border-b border-border-color">
                        <h2 class="text-lg font-semibold text-primary">关于</h2>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">版本</h3>
                            <p class="text-xs text-secondary">听汀 v1.0.0</p>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">开发者</h3>
                            <p class="text-xs text-secondary">听汀团队</p>
                        </div>
                    </div>
                    
                    <div class="settings-item">
                        <div>
                            <h3 class="text-primary">隐私政策</h3>
                        </div>
                        <iconify-icon icon="material-symbols:chevron-right" class="text-secondary"></iconify-icon>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        function settingsApp() {
            return {
                // 主题设置
                currentTheme: 'theme-morning',
                followSystem: true,
                
                // 播放设置
                audioQuality: 'high',
                equalizerEnabled: false,
                equalizerValue: 50,
                gainControl: true,
                
                // 扫描设置
                autoScan: true,
                
                setTheme(theme) {
                    this.currentTheme = theme;
                    window.themeManager.setTheme(theme);
                    this.followSystem = false;
                    console.log('🎨 主题已设置为:', theme);
                },
                
                toggleFollowSystem() {
                    if (this.followSystem) {
                        window.themeManager.resetToSystemTheme();
                        this.currentTheme = window.themeManager.getCurrentTheme();
                        console.log('🔄 跟随系统主题');
                    }
                },
                
                goBack() {
                    window.history.back();
                },

                navigateTo(url) {
                    window.location.href = url;
                },
                
                init() {
                    console.log('🌊 设置页面已加载');
                    
                    // 获取当前主题
                    this.currentTheme = window.themeManager.getCurrentTheme();
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                        this.currentTheme = e.detail.theme;
                    });
                }
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 设置页面');
        });
    </script>
</body>
</html>
