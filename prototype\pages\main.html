<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>听汀 - 音乐播放器</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">

    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>

    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        /* 主界面特殊样式 */
        .main-container {
            background: linear-gradient(135deg, 
                var(--bg-primary) 0%, 
                var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .current-playing-card {
            background: var(--card-bg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-lg);
        }
        
        .album-cover {
            width: 80px;
            height: 80px;
            border-radius: var(--radius-lg);
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }
        
        .progress-bar {
            height: 4px;
            background: var(--border-color);
            border-radius: 2px;
            overflow: hidden;
            position: relative;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            border-radius: 2px;
            transition: width 0.3s ease;
            position: relative;
        }
        
        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 8px;
            height: 100%;
            background: var(--color-accent);
            border-radius: 2px;
            box-shadow: 0 0 8px var(--color-accent);
        }
        
        .song-item {
            transition: all 0.2s ease;
            border-radius: var(--radius-md);
        }
        
        .song-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .anchor-icon {
            color: var(--color-accent);
            transition: all 0.3s ease;
        }
        
        .anchor-icon:hover {
            transform: scale(1.2) rotate(15deg);
        }
        
        .tab-button {
            position: relative;
            padding: 0.75rem 1.5rem;
            border-radius: var(--radius-md);
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            background: var(--card-bg);
            color: var(--color-primary);
            box-shadow: var(--shadow-sm);
        }
        
        .tab-button.active::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--color-accent);
            border-radius: 1px;
        }

        .quick-actions-section {
            margin-top: 2rem;
        }

        .section-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .quick-actions-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 1rem;
        }

        .quick-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem;
            border-radius: 12px;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quick-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
            border-color: var(--color-primary);
        }

        .quick-action-icon {
            font-size: 1.5rem;
            color: var(--color-primary);
        }

        .quick-action-text {
            font-size: 0.75rem;
            color: var(--text-secondary);
            font-weight: 500;
        }
    </style>
</head>
<body class="font-primary">
    <!-- 主题管理器 -->
    <script src="../assets/js/theme.js"></script>
    
    <!-- 主界面 -->
    <div class="main-container water-bg" x-data="mainApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="header-brand">
                    <iconify-icon icon="material-symbols:anchor" class="header-brand-icon"></iconify-icon>
                    <h1 class="header-brand-text">听汀</h1>
                </div>

                <div class="header-actions">
                    <!-- 搜索按钮 -->
                    <button @click="openSearch()" class="header-button" aria-label="搜索">
                        <iconify-icon icon="material-symbols:search" class="text-lg"></iconify-icon>
                    </button>

                    <!-- 主题切换按钮 -->
                    <button
                        data-theme-toggle
                        class="header-theme-toggle"
                        aria-label="切换主题"
                    >
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>

                    <!-- 设置按钮 -->
                    <button @click="openSettings()" class="header-button" aria-label="设置">
                        <iconify-icon icon="material-symbols:settings" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 当前播放卡片 -->
        <div class="px-6 mb-6">
            <div class="current-playing-card p-6 rounded-xl" x-show="currentSong">
                <div class="flex items-center space-x-4 mb-4">
                    <div class="album-cover flex-shrink-0">
                        <iconify-icon icon="material-symbols:music-note" x-show="!currentSong?.cover"></iconify-icon>
                        <img :src="currentSong?.cover" :alt="currentSong?.album" class="w-full h-full object-cover rounded-lg" x-show="currentSong?.cover">
                    </div>
                    
                    <div class="flex-1 min-w-0">
                        <h3 class="font-semibold text-lg text-primary truncate" x-text="currentSong?.title || '未选择歌曲'"></h3>
                        <p class="text-secondary text-sm truncate" x-text="currentSong?.artist || '未知艺术家'"></p>
                        <p class="text-secondary text-xs opacity-75 truncate" x-text="currentSong?.album || '未知专辑'"></p>
                    </div>
                    
                    <button @click="toggleFavorite(currentSong)" class="anchor-icon p-2">
                        <iconify-icon :icon="currentSong?.isFavorite ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'" class="text-xl"></iconify-icon>
                    </button>
                </div>
                
                <!-- 进度条 -->
                <div class="mb-4">
                    <div class="progress-bar">
                        <div class="progress-fill" :style="`width: ${playProgress}%`"></div>
                    </div>
                    <div class="flex justify-between text-xs text-secondary mt-2">
                        <span x-text="formatTime(currentTime)">00:00</span>
                        <span x-text="formatTime(currentSong?.duration || 0)">00:00</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快捷导航 -->
        <div class="px-6 mb-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <a href="./profile.html" class="flex flex-col items-center p-4 bg-card-bg rounded-xl border border-border-color hover:bg-bg-secondary transition-colors">
                    <iconify-icon icon="material-symbols:person" class="text-2xl text-primary mb-2"></iconify-icon>
                    <span class="text-sm font-medium text-primary">我的</span>
                </a>

                <a href="./library.html" class="flex flex-col items-center p-4 bg-card-bg rounded-xl border border-border-color hover:bg-bg-secondary transition-colors">
                    <iconify-icon icon="material-symbols:library-music" class="text-2xl text-primary mb-2"></iconify-icon>
                    <span class="text-sm font-medium text-primary">音乐库</span>
                </a>

                <a href="./anchors.html" class="flex flex-col items-center p-4 bg-card-bg rounded-xl border border-border-color hover:bg-bg-secondary transition-colors">
                    <iconify-icon icon="material-symbols:anchor" class="text-2xl text-primary mb-2"></iconify-icon>
                    <span class="text-sm font-medium text-primary">我的锚点</span>
                </a>

                <a href="./equalizer.html" class="flex flex-col items-center p-4 bg-card-bg rounded-xl border border-border-color hover:bg-bg-secondary transition-colors">
                    <iconify-icon icon="material-symbols:graphic-eq" class="text-2xl text-primary mb-2"></iconify-icon>
                    <span class="text-sm font-medium text-primary">均衡器</span>
                </a>
            </div>
        </div>

        <!-- 标签栏 -->
        <div class="px-6 mb-6">
            <div class="flex space-x-2 bg-bg-secondary rounded-xl p-2">
                <button 
                    @click="activeTab = 'playing'"
                    :class="activeTab === 'playing' ? 'active' : ''"
                    class="tab-button flex-1 text-sm font-medium text-secondary"
                >
                    正在播放
                </button>
                <button 
                    @click="activeTab = 'all'"
                    :class="activeTab === 'all' ? 'active' : ''"
                    class="tab-button flex-1 text-sm font-medium text-secondary"
                >
                    全部歌曲
                </button>
                <button 
                    @click="activeTab = 'albums'"
                    :class="activeTab === 'albums' ? 'active' : ''"
                    class="tab-button flex-1 text-sm font-medium text-secondary"
                >
                    专辑
                </button>
                <button 
                    @click="activeTab = 'favorites'"
                    :class="activeTab === 'favorites' ? 'active' : ''"
                    class="tab-button flex-1 text-sm font-medium text-secondary"
                >
                    收藏
                </button>
            </div>
        </div>
        
        <!-- 歌曲列表 -->
        <div class="px-6 pb-32">
            <div class="space-y-2">
                <template x-for="song in filteredSongs" :key="song.id">
                    <div 
                        @click="playSong(song)"
                        class="song-item p-4 cursor-pointer flex items-center space-x-4"
                        :class="currentSong?.id === song.id ? 'bg-bg-secondary' : ''"
                    >
                        <!-- 封面 -->
                        <div class="w-12 h-12 rounded-lg bg-gradient-to-br from-primary to-accent flex items-center justify-center text-white flex-shrink-0">
                            <iconify-icon icon="material-symbols:music-note" class="text-lg" x-show="!song.cover"></iconify-icon>
                            <img :src="song.cover" :alt="song.album" class="w-full h-full object-cover rounded-lg" x-show="song.cover">
                        </div>
                        
                        <!-- 歌曲信息 -->
                        <div class="flex-1 min-w-0">
                            <h4 class="font-medium text-primary truncate" x-text="song.title"></h4>
                            <p class="text-sm text-secondary truncate">
                                <span x-text="song.artist"></span>
                                <span class="opacity-75"> - </span>
                                <span x-text="song.album" class="opacity-75"></span>
                            </p>
                        </div>
                        
                        <!-- 时长和收藏 -->
                        <div class="flex items-center space-x-3 flex-shrink-0">
                            <span class="text-xs text-secondary" x-text="formatTime(song.duration)"></span>
                            <button 
                                @click.stop="toggleFavorite(song)"
                                class="anchor-icon p-1"
                            >
                                <iconify-icon 
                                    :icon="song.isFavorite ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'" 
                                    class="text-lg"
                                ></iconify-icon>
                            </button>
                        </div>
                    </div>
                </template>
            </div>
        </div>
        
        <!-- 底部播放控制栏 -->
        <div class="bottom-control-container glass-morphism" x-show="currentSong">
            <!-- 迷你播放器卡片 -->
            <div class="mini-player-card">
                <div class="mini-player-cover">
                    <iconify-icon icon="material-symbols:music-note" x-show="!currentSong?.cover"></iconify-icon>
                    <img :src="currentSong?.cover" :alt="currentSong?.album" class="w-full h-full object-cover rounded-xl" x-show="currentSong?.cover">
                </div>

                <div class="mini-player-info">
                    <div class="mini-player-title" x-text="currentSong?.title || '未选择歌曲'"></div>
                    <div class="mini-player-artist" x-text="currentSong?.artist || '未知艺术家'"></div>
                </div>

                <button @click="toggleFavorite(currentSong)" class="header-button" :class="currentSong?.isFavorite ? 'active' : ''">
                    <iconify-icon :icon="currentSong?.isFavorite ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'" class="text-lg"></iconify-icon>
                </button>
            </div>

            <!-- 控制按钮行 -->
            <div class="control-buttons-row">
                <!-- 主要播放控制 -->
                <div class="control-button-group">
                    <button @click="previousSong()" class="control-button">
                        <iconify-icon icon="material-symbols:skip-previous" class="text-xl"></iconify-icon>
                    </button>

                    <button @click="togglePlay()" class="control-button primary">
                        <iconify-icon :icon="isPlaying ? 'material-symbols:pause' : 'material-symbols:play-arrow'" class="text-xl"></iconify-icon>
                    </button>

                    <button @click="nextSong()" class="control-button">
                        <iconify-icon icon="material-symbols:skip-next" class="text-xl"></iconify-icon>
                    </button>
                </div>

                <!-- 次要控制 -->
                <div class="control-button-group">
                    <button @click="toggleShuffle()" class="control-button" :class="isShuffled ? 'active' : ''">
                        <iconify-icon icon="material-symbols:shuffle" class="text-lg"></iconify-icon>
                    </button>

                    <button @click="toggleRepeat()" class="control-button" :class="repeatMode !== 'none' ? 'active' : ''">
                        <iconify-icon :icon="repeatMode === 'one' ? 'material-symbols:repeat-one' : 'material-symbols:repeat'" class="text-lg"></iconify-icon>
                    </button>

                    <button @click="openPlayer()" class="control-button">
                        <iconify-icon icon="material-symbols:fullscreen" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>

            <!-- 快捷操作 -->
            <!-- <div class="quick-actions-section">
                <h3 class="section-title">快捷操作</h3>
                <div class="quick-actions-grid">
                    <div @click="navigateTo('./backup.html')" class="quick-action">
                        <iconify-icon icon="material-symbols:backup" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">数据备份</span>
                    </div>

                    <div @click="navigateTo('./help.html')" class="quick-action">
                        <iconify-icon icon="material-symbols:help" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">帮助</span>
                    </div>

                    <div @click="navigateTo('./features.html')" class="quick-action">
                        <iconify-icon icon="material-symbols:auto-awesome" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">功能</span>
                    </div>

                    <div @click="navigateTo('./contact.html')" class="quick-action">
                        <iconify-icon icon="material-symbols:support-agent" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">客服</span>
                    </div>

                    <div @click="navigateTo('./update.html')" class="quick-action">
                        <iconify-icon icon="material-symbols:system-update" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">更新</span>
                    </div>

                    <div @click="navigateTo('./onboarding.html')" class="quick-action">
                        <iconify-icon icon="material-symbols:tour" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">引导</span>
                    </div>
                </div>
            </div> -->
        </div>
    </div>

    <script>
        function mainApp() {
            return {
                // 播放状态
                isPlaying: false,
                currentSong: null,
                currentTime: 0,
                playProgress: 0,
                isShuffled: false,
                repeatMode: 'none', // 'none', 'all', 'one'
                
                // 界面状态
                activeTab: 'all',
                
                // 示例歌曲数据
                songs: [
                    {
                        id: 1,
                        title: '水调歌头',
                        artist: '古风音乐',
                        album: '诗词歌赋',
                        duration: 245,
                        isFavorite: true,
                        cover: null
                    },
                    {
                        id: 2,
                        title: '汀上白沙',
                        artist: '民谣歌手',
                        album: '江南印象',
                        duration: 198,
                        isFavorite: false,
                        cover: null
                    },
                    {
                        id: 3,
                        title: '静夜思',
                        artist: '古典音乐',
                        album: '唐诗三百首',
                        duration: 167,
                        isFavorite: true,
                        cover: null
                    }
                ],
                
                get filteredSongs() {
                    switch (this.activeTab) {
                        case 'playing':
                            return this.currentSong ? [this.currentSong] : [];
                        case 'favorites':
                            return this.songs.filter(song => song.isFavorite);
                        case 'albums':
                            // 这里可以按专辑分组显示
                            return this.songs;
                        default:
                            return this.songs;
                    }
                },
                
                playSong(song) {
                    this.currentSong = song;
                    this.isPlaying = true;
                    this.currentTime = 0;
                    this.playProgress = 0;
                    console.log('🎵 播放歌曲:', song.title);
                },
                
                togglePlay() {
                    this.isPlaying = !this.isPlaying;
                    console.log(this.isPlaying ? '▶️ 播放' : '⏸️ 暂停');
                },
                
                previousSong() {
                    if (!this.currentSong) return;
                    const currentIndex = this.songs.findIndex(s => s.id === this.currentSong.id);
                    const prevIndex = currentIndex > 0 ? currentIndex - 1 : this.songs.length - 1;
                    this.playSong(this.songs[prevIndex]);
                },
                
                nextSong() {
                    if (!this.currentSong) return;
                    const currentIndex = this.songs.findIndex(s => s.id === this.currentSong.id);
                    const nextIndex = currentIndex < this.songs.length - 1 ? currentIndex + 1 : 0;
                    this.playSong(this.songs[nextIndex]);
                },
                
                toggleFavorite(song) {
                    if (!song) return;
                    song.isFavorite = !song.isFavorite;
                    console.log(song.isFavorite ? '⚓ 已收藏' : '○ 取消收藏', song.title);
                },
                
                toggleShuffle() {
                    this.isShuffled = !this.isShuffled;
                    console.log(this.isShuffled ? '🔀 随机播放' : '📋 顺序播放');
                },
                
                toggleRepeat() {
                    const modes = ['none', 'all', 'one'];
                    const currentIndex = modes.indexOf(this.repeatMode);
                    this.repeatMode = modes[(currentIndex + 1) % modes.length];
                    console.log('🔁 重复模式:', this.repeatMode);
                },
                
                formatTime(seconds) {
                    const mins = Math.floor(seconds / 60);
                    const secs = Math.floor(seconds % 60);
                    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                },
                
                openPlayer() {
                    window.location.href = './player.html';
                },
                
                openSettings() {
                    window.location.href = './settings.html';
                },

                openSearch() {
                    window.location.href = './search.html';
                },
                
                init() {
                    console.log('🌊 主界面已加载');
                    
                    // 模拟播放进度
                    setInterval(() => {
                        if (this.isPlaying && this.currentSong) {
                            this.currentTime += 1;
                            this.playProgress = (this.currentTime / this.currentSong.duration) * 100;
                            
                            if (this.currentTime >= this.currentSong.duration) {
                                this.nextSong();
                            }
                        }
                    }, 1000);
                    
                    // 默认播放第一首歌
                    if (this.songs.length > 0) {
                        this.currentSong = this.songs[0];
                    }

                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 主界面');
        });
    </script>
</body>
</html>
