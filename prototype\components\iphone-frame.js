/**
 * iPhone外壳Web组件
 * 高保真原型设计的设备框架组件
 */
class IPhoneFrame extends HTMLElement {
  // 默认配置
  static get defaultOptions() {
    return {
      contentUrl: 'about:blank',
      showStatusBar: true,
      scale: '1',
      model: 'iphone15pro',
      statusBarStyle: 'auto', // auto, light, dark
      batteryLevel: 100,
      showTime: true,
      showHomeIndicator: false,
      signalStrength: 4, // 1-4
      wifiStrength: 3, // 1-3
      interactionEffects: false,
      showDeviceFrame: false // 新增：是否显示设备外壳
    };
  }

  // 定义所观察的属性
  static get observedAttributes() {
    return [
      'content-url', 
      'show-status-bar', 
      'scale', 
      'model', 
      'status-bar-style', 
      'battery-level',
      'show-time',
      'show-home-indicator',
      'signal-strength',
      'wifi-strength',
      'interaction-effects',
      'show-device-frame' // 新增属性
    ];
  }

  constructor() {
    super();
    this.attachShadow({ mode: 'open' });
    
    // 初始化配置
    this.options = { ...IPhoneFrame.defaultOptions };
    
    // 绑定方法到实例
    this.updateTime = this.updateTime.bind(this);
    this.updateBatteryIcon = this.updateBatteryIcon.bind(this);
    this.handleThemeChange = this.handleThemeChange.bind(this);
    
    // 存储事件监听器引用
    this.eventListeners = {};
    
    // 定时器引用，用于清理
    this.timeInterval = null;
    
    // 观察者引用
    this.themeObserver = null;
  }

  // 当元素被添加到DOM时调用
  connectedCallback() {
    // 从属性中解析配置
    this.parseAttributes();
    
    // 渲染组件
    this.render();
    
    // 添加事件监听器
    this.addEventListeners();
    
    // 启动时间更新
    if (this.options.showTime && this.options.showStatusBar) {
      this.updateTime();
      this.timeInterval = setInterval(this.updateTime, 60000);
    }
    
    // 监听主题变化
    this.observeThemeChanges();
  }
  
  // 当元素从DOM中移除时调用
  disconnectedCallback() {
    // 清理定时器
    if (this.timeInterval) {
      clearInterval(this.timeInterval);
      this.timeInterval = null;
    }
    
    // 移除所有事件监听器
    this.removeAllEventListeners();
    
    // 停止主题观察
    if (this.themeObserver) {
      this.themeObserver.disconnect();
      this.themeObserver = null;
    }
  }
  
  // 当观察的属性变化时调用
  attributeChangedCallback(name, oldValue, newValue) {
    if (oldValue === newValue) return;
    
    const propName = this.attrToPropName(name);
    
    // 特殊处理布尔值
    if (['showStatusBar', 'showTime', 'showHomeIndicator', 'interactionEffects', 'showDeviceFrame'].includes(propName)) {
      this.options[propName] = newValue !== 'false';
    } 
    // 特殊处理数字值
    else if (['batteryLevel', 'signalStrength', 'wifiStrength'].includes(propName)) {
      this.options[propName] = parseInt(newValue, 10) || this.options[propName];
    }
    // 其他值直接赋值
    else {
      this.options[propName] = newValue || this.options[propName];
    }
    
    // 重新渲染组件
    this.render();
    
    // 如果 status-bar-style 或 showStatusBar 变化，更新 iframe 主题
    if (propName === 'statusBarStyle' || propName === 'showStatusBar') {
      const iframe = this.shadowRoot.querySelector('iframe');
      if (iframe && iframe.contentDocument) {
        this._updateIframeTheme(iframe.contentDocument, this.options.statusBarStyle);
      }
    }
    
    // 重新启动时间更新（如果需要）
    if (propName === 'showTime' || propName === 'showStatusBar') {
      if (this.timeInterval) {
        clearInterval(this.timeInterval);
        this.timeInterval = null;
      }
      
      if (this.options.showTime && this.options.showStatusBar) {
        this.updateTime();
        this.timeInterval = setInterval(this.updateTime, 60000);
      }
    }
  }
  
  // 将连字符属性名转换为驼峰属性名
  attrToPropName(attr) {
    return attr.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
  }
  
  // 解析HTML属性
  parseAttributes() {
    // 处理属性
    this.options.contentUrl = this.getAttribute('content-url') || this.options.contentUrl;
    this.options.showStatusBar = this.hasAttribute('show-status-bar') ? this.getAttribute('show-status-bar') === 'true' : IPhoneFrame.defaultOptions.showStatusBar;
    this.options.scale = this.getAttribute('scale') || this.options.scale;
    this.options.model = this.getAttribute('model') || this.options.model;
    this.options.statusBarStyle = this.getAttribute('status-bar-style') || this.options.statusBarStyle;
    this.options.batteryLevel = parseInt(this.getAttribute('battery-level'), 10) || this.options.batteryLevel;
    this.options.showTime = this.hasAttribute('show-time') ? this.getAttribute('show-time') === 'true' : IPhoneFrame.defaultOptions.showTime;
    this.options.showHomeIndicator = this.hasAttribute('show-home-indicator') ? this.getAttribute('show-home-indicator') === 'true' : IPhoneFrame.defaultOptions.showHomeIndicator;
    this.options.signalStrength = parseInt(this.getAttribute('signal-strength'), 10) || this.options.signalStrength;
    this.options.wifiStrength = parseInt(this.getAttribute('wifi-strength'), 10) || this.options.wifiStrength;
    this.options.interactionEffects = this.hasAttribute('interaction-effects') ? this.getAttribute('interaction-effects') === 'true' : IPhoneFrame.defaultOptions.interactionEffects;
    this.options.showDeviceFrame = this.hasAttribute('show-device-frame') ? this.getAttribute('show-device-frame') === 'true' : IPhoneFrame.defaultOptions.showDeviceFrame; // 新增属性处理
  }

  // 添加交互效果事件监听器
  addInteractionListeners() {
    const frame = this.shadowRoot.querySelector('.iphone-frame');
    if (!frame || !this.options.interactionEffects) return;

    // 存储事件监听器引用以便后续移除
    this.eventListeners = {
      mouseDown: () => this.addInteractionClass('active'),
      mouseUp: () => this.removeInteractionClass('active'),
      mouseLeave: () => this.removeInteractionClass('active')
    };

    frame.addEventListener('mousedown', this.eventListeners.mouseDown);
    frame.addEventListener('mouseup', this.eventListeners.mouseUp);
    frame.addEventListener('mouseleave', this.eventListeners.mouseLeave);
  }

  // 移除交互效果事件监听器
  removeInteractionListeners() {
    const frame = this.shadowRoot.querySelector('.iphone-frame');
    if (!frame || !this.eventListeners) return;

    frame.removeEventListener('mousedown', this.eventListeners.mouseDown);
    frame.removeEventListener('mouseup', this.eventListeners.mouseUp);
    frame.removeEventListener('mouseleave', this.eventListeners.mouseLeave);
    
    this.eventListeners = {};
  }

  // 添加事件监听器
  addEventListeners() {
    this.addInteractionListeners();
  }

  // 移除事件监听器
  removeEventListeners() {
    this.removeInteractionListeners();
  }

  // 移除所有事件监听器
  removeAllEventListeners() {
    this.removeInteractionListeners();
  }
  
  // 添加交互类
  addInteractionClass(className) {
    const frame = this.shadowRoot.querySelector('.iphone-frame');
    if (frame) {
      frame.classList.add(className);
    }
  }
  
  // 移除交互类
  removeInteractionClass(className) {
    const frame = this.shadowRoot.querySelector('.iphone-frame');
    if (frame) {
      frame.classList.remove(className);
    }
  }
  
  // 更新时间显示
  updateTime() {
    const timeElement = this.shadowRoot.querySelector('.time');
    if (timeElement) {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      timeElement.textContent = `${hours}:${minutes}`;
    }
  }
  
  // 更新电池图标
  updateBatteryIcon() {
    const batteryLevel = this.options.batteryLevel;
    let batteryIcon = 'ion:battery-full';
    
    if (batteryLevel <= 20) {
      batteryIcon = 'ion:battery-dead';
    } else if (batteryLevel <= 40) {
      batteryIcon = 'ion:battery-half';
    }
    
    const batteryElement = this.shadowRoot.querySelector('.battery-icon iconify-icon');
    if (batteryElement) {
      batteryElement.setAttribute('icon', batteryIcon);
    }
  }
  
  // 获取信号强度图标
  getSignalIcon() {
    const strength = this.options.signalStrength;
    if (strength <= 1) return 'ion:cellular-outline';
    if (strength === 2) return 'ion:cellular-sharp';
    if (strength === 3) return 'ion:cellular';
    return 'ion:cellular';
  }
  
  // 获取WiFi强度图标
  getWifiIcon() {
    const strength = this.options.wifiStrength;
    if (strength <= 1) return 'ion:wifi-outline';
    if (strength === 2) return 'ion:wifi-sharp';
    return 'ion:wifi';
  }
  
  // 获取设备尺寸规格
  getDeviceSpecs() {
    const models = {
      iphone15pro: {
        width: 430,
        height: 932,
        borderRadius: 55,
        borderWidth: 14,
        islandWidth: 126,
        islandHeight: 37,
        homeIndicatorWidth: 134
      },
      iphone14: {
        width: 390,
        height: 844,
        borderRadius: 47,
        borderWidth: 12,
        notchWidth: 160,
        notchHeight: 34,
        homeIndicatorWidth: 134
      },
      iphone13: {
        width: 390,
        height: 844,
        borderRadius: 47,
        borderWidth: 12,
        notchWidth: 160,
        notchHeight: 34,
        homeIndicatorWidth: 134
      },
      iphonese: {
        width: 375,
        height: 667,
        borderRadius: 40,
        borderWidth: 12,
        homeButtonSize: 60,
        bezels: {
          top: 60,
          bottom: 60
        }
      },
      ipad: {
        width: 820,
        height: 1180,
        borderRadius: 20,
        borderWidth: 16,
        bezels: {
          top: 20,
          bottom: 20,
          left: 20,
          right: 20
        }
      }
    };
    
    return models[this.options.model] || models.iphone15pro;
  }
  
  // 获取设备类型
  getDeviceType() {
    const model = this.options.model.toLowerCase();
    if (model.includes('ipad')) return 'ipad';
    
    const seModels = ['iphonese', 'iphone8', 'iphone7', 'iphone6'];
    if (seModels.some(m => model.includes(m))) return 'classic';
    
    const notchModels = ['iphone13', 'iphone14', 'iphonexr', 'iphonexs'];
    if (notchModels.some(m => model.includes(m))) return 'notch';
    
    return 'dynamicIsland';
  }
  
  // 计算容器尺寸
  calculateContainerSize() {
    const specs = this.getDeviceSpecs();
    const scale = parseFloat(this.options.scale) || 1;
    
    // 计算缩放后的尺寸，确保考虑边框和边距
    const scaledWidth = specs.width * scale;
    const scaledHeight = specs.height * scale;
    
    return {
      width: scaledWidth,
      height: scaledHeight
    };
  }
  
  // 生成设备样式
  generateDeviceStyles() {
    const specs = this.getDeviceSpecs();
    const type = this.getDeviceType();
    const { scale } = this.options;
    const containerSize = this.calculateContainerSize();
    
    return `
      /* 组件容器 - 适应缩放尺寸 */
      :host {
        display: block;
        width: ${containerSize.width}px;
        height: ${containerSize.height}px;
        position: relative;
        overflow: visible;
        box-sizing: content-box;
      }
      
      /* iPhone容器样式 */
      .iphone-container {
        position: absolute;
        width: ${specs.width}px;
        height: ${specs.height}px;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) scale(${scale});
        transform-origin: center center;
        box-sizing: border-box;
      }
      
      .iphone-frame {
        width: 100%;
        height: 100%;
        background-color: #ffffff;
        border-radius: ${specs.borderRadius}px;
        overflow: hidden;
        position: relative;
        box-shadow: 
          0 50px 100px -20px rgba(50,50,93,0.25),
          0 30px 60px -30px rgba(0,0,0,0.3),
          inset 0 -2px 6px 0 rgba(10,37,64,0.35);
        border: ${specs.borderWidth}px solid #1c1c1e;
        box-sizing: border-box;
        transition: transform 0.15s ease, box-shadow 0.15s ease;
      }
      
      .has-status-bar .screen-content {
        top: 47px;
      }
      
      /* 交互效果 */
      .iphone-frame.active {
        transform: scale(0.98);
        box-shadow: 
          0 30px 60px -30px rgba(50,50,93,0.25),
          0 15px 30px -15px rgba(0,0,0,0.3),
          inset 0 -1px 3px 0 rgba(10,37,64,0.35);
      }

      /* 状态栏 */
      .status-bar {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 47px;
        display: flex;
        padding: 0 28px;
        justify-content: space-between;
        align-items: center;
        font-size: 15px;
        font-weight: 600;
        z-index: 900;
        color: #000000;
      }
      
      ${type === 'dynamicIsland' ? `
      /* 动态岛 */
      .dynamic-island {
        position: absolute;
        top: 12px;
        left: 50%;
        transform: translateX(-50%);
        width: ${specs.islandWidth}px;
        height: ${specs.islandHeight}px;
        background-color: #000000;
        border-radius: 24px;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      /* 摄像头和传感器 */
      .sensors {
        width: 10px;
        height: 10px;
        background-color: #1a1a1a;
        border-radius: 50%;
        position: relative;
      }
      
      .sensors:before {
        content: "";
        position: absolute;
        right: -20px;
        width: 8px;
        height: 8px;
        background-color: #1a1a1a;
        border-radius: 50%;
      }
      ` : type === 'notch' ? `
      /* 刘海区域 */
      .notch {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: ${specs.notchWidth}px;
        height: ${specs.notchHeight}px;
        background-color: #000000;
        border-bottom-left-radius: 18px;
        border-bottom-right-radius: 18px;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      /* 摄像头和传感器 */
      .sensors {
        width: 10px;
        height: 10px;
        background-color: #1a1a1a;
        border-radius: 50%;
        position: relative;
      }
      
      .sensors:before {
        content: "";
        position: absolute;
        right: -20px;
        width: 8px;
        height: 8px;
        background-color: #1a1a1a;
        border-radius: 50%;
      }
      ` : type === 'classic' ? `
      /* 经典iPhone带Home按钮 */
      .home-button {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: ${specs.homeButtonSize}px;
        height: ${specs.homeButtonSize}px;
        border-radius: 50%;
        border: 1px solid #b2b2b2;
        background-color: #f9f9f9;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .home-button::after {
        content: "";
        width: 15px;
        height: 15px;
        border: 1px solid #b2b2b2;
        border-radius: 2px;
      }
      ` : ``}
      
      /* 状态栏左侧 */
      .status-left .time {
        font-size: 16px;
        font-weight: bold;
      }
      
      /* 状态栏右侧 */
      .status-right {
        display: flex;
        align-items: center;
        gap: 8px;
      }
      
      /* 状态栏图标样式 */
      .status-right iconify-icon {
        font-size: 16px;
      }
      
      /* 内容区域 */
      .screen-content {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 100;
        background-color: #fff;
        overflow: hidden;
      }
      
      /* iframe样式 */
      #content-frame {
        width: 100%;
        height: 100%;
        border: none;
        border-radius: inherit;
        background-color: #fff;
        overflow: hidden; /* 隐藏 iframe 的滚动条 */
      }
      
      /* 底部指示条 */
      .home-indicator {
        position: absolute;
        bottom: 10px;
        left: 50%;
        transform: translateX(-50%);
        width: ${specs.homeIndicatorWidth || 134}px;
        height: 5px;
        background-color: #000000;
        border-radius: 100px;
        z-index: 900;
      }
      
      /* 状态栏样式 - 根据设置决定颜色 */
      ${this.options.statusBarStyle === 'light' ? `
        .status-bar {
          background-color: #fefeff;
          color: #000000;
        }
        
        .status-right iconify-icon {
          color: #000000;
        }
      ` : this.options.statusBarStyle === 'dark' ? `
        .status-bar {
          background-color: #181b21;
          color: #ffffff;
        }
        
        .status-right iconify-icon {
          color: #ffffff;
        }
      ` : `
        /* 自动根据媒体查询决定状态栏样式 */
        .status-bar {
          background-color: #fefeff; /* 默认亮色背景 */
          color: #000000; /* 默认亮色字体 */
        }
        .status-right iconify-icon {
          color: #000000; /* 默认亮色图标 */
        }
        @media (prefers-color-scheme: dark) {
          .status-bar {
            background-color: #181b21;
            color: #ffffff;
          }
          
          .status-right iconify-icon {
            color: #ffffff;
          }
        }
      `}

      /**  无外壳 */
      .not-frame {
        border: 0;
        border-radius: 2px;
      }
      
      .not-frame .dynamic-island,
      .not-frame .notch {
        display: none;
      }
    `;
  }
  
  // 生成设备HTML结构
  generateDeviceHTML() {
    const { 
      contentUrl, 
      showStatusBar, 
      showHomeIndicator,
      showTime,
      showDeviceFrame // 从配置中获取新属性
    } = this.options;
    
    const deviceType = this.getDeviceType();
    
    // 如果不显示设备框架，则只返回屏幕内容
    const noFrameCls = !showDeviceFrame ? 'not-frame' : '';

    return `
      <div class="iphone-container">
        <div class="iphone-frame ${noFrameCls} ${showStatusBar ? 'has-status-bar' : ''}">
          ${deviceType === 'dynamicIsland' && showStatusBar ? this.generateDynamicIsland() :
             deviceType === 'notch' && showStatusBar ? this.generateNotch() : 
             deviceType === 'classic' ? this.generateClassicHomeButton() : ''}
          
          ${showStatusBar ? this.generateStatusBar() : ''}
          
          <div class="screen-content">
            <iframe id="content-frame" src="${contentUrl}" frameborder="0"></iframe>
          </div>
          
          ${showHomeIndicator && deviceType !== 'classic' ? `
            <div class="home-indicator"></div>
          ` : ''}
        </div>
      </div>
    `;
  }
  
  // 生成状态栏HTML
  generateStatusBar() {
    const { showTime, signalStrength, wifiStrength } = this.options;
    return `
      <div class="status-bar">
        <div class="status-left">
          ${showTime ? `<span class="time">9:41</span>` : ''}
        </div>
        
        <div class="status-right">
          <iconify-icon icon="${this.getSignalIcon()}"></iconify-icon>
          <iconify-icon icon="${this.getWifiIcon()}"></iconify-icon>
          <iconify-icon icon="ion:battery-full" style="margin-left: 3px; transform: scale(1.3);"></iconify-icon>
        </div>
      </div>
    `;
  }
  
  // 生成经典Home按钮
  generateClassicHomeButton() {
    const specs = this.getDeviceSpecs();
    return `
      <div class="home-button">
        <div class="home-button__inner"></div>
      </div>
    `;
  }
  
  // 动态岛生成方法
  generateDynamicIsland() {
    const specs = this.getDeviceSpecs();
    return `
      <div class="dynamic-island">
        <div class="sensors"></div>
      </div>
    `;
  }
  
  // 刘海区域生成方法
  generateNotch() {
    const specs = this.getDeviceSpecs();
    return `
      <div class="notch">
        <div class="sensors"></div>
      </div>
    `;
  }
  
  // 监听主题变化
  observeThemeChanges() {
    // 如果已经存在观察者，先停止
    if (this.themeObserver) {
      this.themeObserver.disconnect();
    }

    // 创建新的MutationObserver
    this.themeObserver = new MutationObserver(this.handleThemeChange);
    
    // 开始观察documentElement的class属性变化
    this.themeObserver.observe(document.documentElement, { attributes: true, attributeFilter: ['class'] });
  }

  // 处理主题变化
  handleThemeChange(mutations) {
    for (const mutation of mutations) {
      if (mutation.attributeName === 'class') {
        const iframe = this.shadowRoot.querySelector('iframe');
        // 只有当 iframe 加载且可访问时才更新
        if (iframe && iframe.contentWindow && iframe.contentDocument) {
          try {
            const isDarkMode = document.documentElement.classList.contains('dark');
            if (isDarkMode) {
              iframe.contentDocument.documentElement.classList.add('dark');
            } else {
              iframe.contentDocument.documentElement.classList.remove('dark');
            }

          } catch (e) {
            // 对于跨域iframe，contentDocument访问会被阻止
            if (e instanceof DOMException && (e.name === 'SecurityError' || e.name === 'NotAllowedError')) {
              console.warn('无法访问跨域iframe的内容:', e);
            } else {
              console.warn('无法同步iframe主题:', e);
            }
          }
        }
      }
    }
  }
  
  // 同步主题到iframe
  syncThemeWithIframe(iframe) {
    if (!iframe) return;
    
    // 使用once选项确保事件监听器只执行一次，避免重复注入
    iframe.addEventListener('load', () => {
      try {
        const isDarkMode = document.documentElement.classList.contains('dark');
        const iframeDoc = iframe.contentDocument;
        if (!iframeDoc) {
            console.warn('iframe contentDocument is not available after load.');
            return;
        }

        // 同步iframe内部的主题
        if (isDarkMode) {
          iframeDoc.documentElement.classList.add('dark');
        } else {
          iframeDoc.documentElement.classList.remove('dark');
        }

        // 注入自定义样式以隐藏滚动条（保留滚动功能）
        const style = iframeDoc.createElement('style');
        style.innerHTML = `
          /* Webkit 浏览器隐藏滚动条 */
          ::-webkit-scrollbar {
            display: none !important;
            width: 0 !important;
            height: 0 !important;
          }
          /* Firefox 隐藏滚动条 */
          html {
            scrollbar-width: none !important; 
          }
          /* IE and Edge 隐藏滚动条 */
          body {
            -ms-overflow-style: none !important;  
          }
          /* 隐藏状态栏 */
          .status-bar {
            display: none !important;
          }
        `;
        iframeDoc.head.appendChild(style);

      } catch (e) {
        // 对于跨域iframe，contentDocument访问会被阻止
        if (e instanceof DOMException && (e.name === 'SecurityError' || e.name === 'NotAllowedError')) {
          console.warn('无法访问跨域iframe的内容:', e);
        } else {
          console.warn('无法同步iframe主题:', e);
        }
      }
    }, { once: true }); // 使用 once: true 确保只执行一次
  }
  
  // 渲染组件
  render() {
    // 只有当组件已连接到DOM时才进行渲染
    if (!this.isConnected) return;
    
    // 如果shadowRoot不存在，直接返回
    if (!this.shadowRoot) return;
    
    // 第一次渲染或重要配置更改时更新样式
    if (!this.styleElement) {
      this.styleElement = document.createElement('style');
      this.shadowRoot.appendChild(this.styleElement);
    }
    
    // 更新样式
    this.styleElement.textContent = this.generateDeviceStyles();
    
    // 获取现有iframe（如果存在）
    const existingIframe = this.shadowRoot.querySelector('#content-frame');
    
    // 如果没有现有iframe或URL已更改，则更新HTML
    if (!existingIframe || existingIframe.src !== this.options.contentUrl) {
      // 创建临时容器来解析HTML
      const tempContainer = document.createElement('div');
      tempContainer.innerHTML = this.generateDeviceHTML();
      
      // 获取新生成的iframe元素
      const newIframe = tempContainer.querySelector('#content-frame');
      
      // 将新内容插入shadowRoot
      this.shadowRoot.innerHTML = '';
      this.shadowRoot.appendChild(this.styleElement);
      
      // 将新生成的内容添加进去
      if (tempContainer.firstChild) {
        while (tempContainer.firstChild) {
          this.shadowRoot.appendChild(tempContainer.firstChild);
        }
      } else {
        // 如果tempContainer没有子节点（不应该发生），则直接插入HTML
        this.shadowRoot.innerHTML = `<style>${this.styleElement.textContent}</style>${this.generateDeviceHTML()}`;
      }
      
      // 设置 iframe 内容和主题/滚动条（在加载时）
      const newIframeElement = this.shadowRoot.querySelector('#content-frame');
      if (newIframeElement) {
        this.syncThemeWithIframe(newIframeElement);
      }
    } else {
      // 如果 iframe 已经存在且 URL 没有改变，确保主题是最新的
      if (existingIframe.contentDocument) {
        this._updateIframeTheme(existingIframe.contentDocument, this.options.statusBarStyle);
      }
    }
    
    // 更新电池图标
    this.updateBatteryIcon();
  }
}

// 注册自定义元素
customElements.define('iphone-frame', IPhoneFrame);