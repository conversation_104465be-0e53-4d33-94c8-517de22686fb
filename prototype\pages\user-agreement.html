<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户协议 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .agreement-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .agreement-content {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .agreement-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .agreement-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--color-primary);
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .agreement-meta {
            text-align: center;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            padding-bottom: 2rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .agreement-section {
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
            padding-left: 1rem;
            border-left: 4px solid var(--color-accent);
        }
        
        .section-content {
            color: var(--text-secondary);
            line-height: 1.8;
            margin-bottom: 1rem;
        }
        
        .section-list {
            list-style: none;
            padding-left: 1rem;
        }
        
        .section-list li {
            position: relative;
            margin-bottom: 0.5rem;
            padding-left: 1.5rem;
        }
        
        .section-list li::before {
            content: '•';
            color: var(--color-accent);
            font-weight: bold;
            position: absolute;
            left: 0;
        }
        
        .highlight {
            background: linear-gradient(90deg, var(--color-primary)/10, var(--color-accent)/10);
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid var(--color-primary);
            margin: 1rem 0;
        }
        
        .contact-info {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 1.5rem;
            margin-top: 2rem;
            border: 1px solid var(--border-color);
        }
        
        .contact-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }
        
        .toc {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            border: 1px solid var(--border-color);
        }
        
        .toc-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }
        
        .toc-list {
            list-style: none;
        }
        
        .toc-list li {
            margin-bottom: 0.5rem;
        }
        
        .toc-list a {
            color: var(--color-primary);
            text-decoration: none;
            transition: color 0.3s ease;
        }
        
        .toc-list a:hover {
            color: var(--color-accent);
            text-decoration: underline;
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="agreement-container water-bg" x-data="agreementApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">用户协议</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="printAgreement()" class="header-button" aria-label="打印">
                        <iconify-icon icon="material-symbols:print" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 协议内容 -->
        <div class="px-6 py-8">
            <div class="agreement-content">
                <h1 class="agreement-title">听汀用户服务协议</h1>
                
                <div class="agreement-meta">
                    <p>生效日期：2024年7月19日</p>
                    <p>版本：v3.0</p>
                </div>
                
                <!-- 目录 -->
                <div class="toc">
                    <div class="toc-title">目录</div>
                    <ul class="toc-list">
                        <li><a href="#section1">1. 协议的接受</a></li>
                        <li><a href="#section2">2. 服务说明</a></li>
                        <li><a href="#section3">3. 用户权利与义务</a></li>
                        <li><a href="#section4">4. 隐私保护</a></li>
                        <li><a href="#section5">5. 知识产权</a></li>
                        <li><a href="#section6">6. 免责声明</a></li>
                        <li><a href="#section7">7. 协议变更</a></li>
                        <li><a href="#section8">8. 联系我们</a></li>
                    </ul>
                </div>
                
                <!-- 协议条款 -->
                <div class="agreement-section" id="section1">
                    <h2 class="section-title">1. 协议的接受</h2>
                    <div class="section-content">
                        <p>欢迎使用听汀音乐播放器！本协议是您与听汀开发团队之间关于使用听汀服务的法律协议。</p>
                        <div class="highlight">
                            <strong>重要提示：</strong>请您仔细阅读本协议的全部条款。您使用听汀服务即表示您已阅读、理解并同意接受本协议的全部条款。
                        </div>
                    </div>
                </div>
                
                <div class="agreement-section" id="section2">
                    <h2 class="section-title">2. 服务说明</h2>
                    <div class="section-content">
                        <p>听汀是一款专注于本地音乐播放的应用程序，提供以下服务：</p>
                        <ul class="section-list">
                            <li>本地音乐文件播放和管理</li>
                            <li>播放列表创建和编辑</li>
                            <li>音乐收藏和分类</li>
                            <li>个性化音乐体验</li>
                            <li>数据备份和同步</li>
                        </ul>
                    </div>
                </div>
                
                <div class="agreement-section" id="section3">
                    <h2 class="section-title">3. 用户权利与义务</h2>
                    <div class="section-content">
                        <h4 style="color: var(--text-primary); margin: 1rem 0 0.5rem 0;">用户权利：</h4>
                        <ul class="section-list">
                            <li>免费使用听汀的基本功能</li>
                            <li>享受无广告的纯净体验</li>
                            <li>控制个人数据的使用</li>
                            <li>获得技术支持和帮助</li>
                        </ul>
                        
                        <h4 style="color: var(--text-primary); margin: 1rem 0 0.5rem 0;">用户义务：</h4>
                        <ul class="section-list">
                            <li>确保使用的音乐文件具有合法版权</li>
                            <li>不得将听汀用于任何违法目的</li>
                            <li>保护账户安全，不得与他人共享</li>
                            <li>尊重其他用户的权利</li>
                        </ul>
                    </div>
                </div>
                
                <div class="agreement-section" id="section4">
                    <h2 class="section-title">4. 隐私保护</h2>
                    <div class="section-content">
                        <p>我们高度重视您的隐私保护：</p>
                        <ul class="section-list">
                            <li>不会收集您的个人敏感信息</li>
                            <li>本地数据存储，您拥有完全控制权</li>
                            <li>可选择性开启数据同步功能</li>
                            <li>详细隐私政策请查看《隐私政策》</li>
                        </ul>
                    </div>
                </div>
                
                <div class="agreement-section" id="section5">
                    <h2 class="section-title">5. 知识产权</h2>
                    <div class="section-content">
                        <p>听汀应用程序的所有知识产权归开发团队所有。用户仅获得使用许可，不得：</p>
                        <ul class="section-list">
                            <li>复制、修改或反编译应用程序</li>
                            <li>将应用程序用于商业目的</li>
                            <li>移除或修改版权声明</li>
                        </ul>
                    </div>
                </div>
                
                <div class="agreement-section" id="section6">
                    <h2 class="section-title">6. 免责声明</h2>
                    <div class="section-content">
                        <div class="highlight">
                            听汀按"现状"提供服务，不对服务的连续性、准确性或完整性做出保证。用户使用听汀服务的风险由用户自行承担。
                        </div>
                    </div>
                </div>
                
                <div class="agreement-section" id="section7">
                    <h2 class="section-title">7. 协议变更</h2>
                    <div class="section-content">
                        <p>我们可能会不时更新本协议。重大变更将通过应用内通知的方式告知用户。继续使用服务即表示接受更新后的协议。</p>
                    </div>
                </div>
                
                <div class="agreement-section" id="section8">
                    <h2 class="section-title">8. 联系我们</h2>
                    <div class="contact-info">
                        <div class="contact-title">如有任何问题，请联系我们：</div>
                        <div class="contact-item">
                            <iconify-icon icon="material-symbols:email" class="text-accent"></iconify-icon>
                            <span>邮箱：<EMAIL></span>
                        </div>
                        <div class="contact-item">
                            <iconify-icon icon="material-symbols:language" class="text-accent"></iconify-icon>
                            <span>官网：www.tingting.app</span>
                        </div>
                        <div class="contact-item">
                            <iconify-icon icon="material-symbols:location-on" class="text-accent"></iconify-icon>
                            <span>地址：中国·北京</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function agreementApp() {
            return {
                printAgreement() {
                    window.print();
                    console.log('🖨️ 打印用户协议');
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('📜 用户协议页面已加载');
                    
                    // 平滑滚动到锚点
                    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                        anchor.addEventListener('click', function (e) {
                            e.preventDefault();
                            const target = document.querySelector(this.getAttribute('href'));
                            if (target) {
                                target.scrollIntoView({
                                    behavior: 'smooth',
                                    block: 'start'
                                });
                            }
                        });
                    });
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 用户协议');
        });
    </script>
</body>
</html>
