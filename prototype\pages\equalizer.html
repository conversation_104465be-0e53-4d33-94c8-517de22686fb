<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音律调和 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .equalizer-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .eq-band {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0 0.75rem;
            min-width: 60px;
        }

        .eq-slider {
            writing-mode: bt-lr;
            -webkit-appearance: slider-vertical;
            width: 12px;
            height: 240px;
            background: linear-gradient(to top, var(--border-color), var(--bg-secondary));
            border-radius: 6px;
            outline: none;
            margin: 1.5rem 0;
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .eq-slider:hover {
            background: linear-gradient(to top, var(--color-primary)/20, var(--bg-secondary));
        }
        
        .eq-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-accent), #f4d03f);
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
            border: 2px solid white;
            transition: all 0.3s ease;
        }

        .eq-slider::-webkit-slider-thumb:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(212, 175, 55, 0.6);
        }

        .eq-slider::-moz-range-thumb {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-accent), #f4d03f);
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(212, 175, 55, 0.4);
            border: 2px solid white;
            transition: all 0.3s ease;
        }
        
        .eq-frequency {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
        }
        
        .eq-value {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-top: 0.5rem;
            min-width: 3rem;
            text-align: center;
        }
        
        .preset-card {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .preset-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }
        
        .preset-card.active {
            border-color: var(--color-primary);
            background: rgba(74, 144, 226, 0.1);
        }
        
        .preset-icon {
            font-size: 2rem;
            color: var(--color-primary);
            margin-bottom: 0.5rem;
        }
        
        .preset-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }
        
        .preset-desc {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }
        
        .waveform-container {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            height: 120px;
            position: relative;
            overflow: hidden;
        }
        
        .waveform-bar {
            display: inline-block;
            width: 3px;
            margin: 0 1px;
            background: linear-gradient(to top, var(--color-primary), var(--color-accent));
            border-radius: 2px;
            animation: wave-animation 1.5s ease-in-out infinite;
        }
        
        @keyframes wave-animation {
            0%, 100% { height: 20%; }
            50% { height: 80%; }
        }
        
        .audio-controls {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }
        
        .control-group {
            margin-bottom: 1.5rem;
        }
        
        .control-label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .control-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: var(--border-color);
            outline: none;
            -webkit-appearance: none;
        }
        
        .control-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: var(--color-primary);
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(74, 144, 226, 0.3);
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="equalizer-container water-bg" x-data="equalizerApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">音律调和</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="resetEqualizer()" class="header-button" aria-label="重置">
                        <iconify-icon icon="material-symbols:refresh" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button @click="savePreset()" class="header-button" aria-label="保存预设">
                        <iconify-icon icon="material-symbols:save" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 均衡器内容 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto space-y-8">
                <!-- 预设模式 -->
                <div>
                    <h2 class="title-md text-primary mb-4">预设模式</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
                        <template x-for="preset in presets" :key="preset.id">
                            <div @click="applyPreset(preset)" 
                                 :class="currentPreset?.id === preset.id ? 'active' : ''" 
                                 class="preset-card">
                                <iconify-icon :icon="preset.icon" class="preset-icon"></iconify-icon>
                                <div class="preset-name" x-text="preset.name"></div>
                                <div class="preset-desc" x-text="preset.description"></div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- 图形均衡器 -->
                <div>
                    <h2 class="title-md text-primary mb-4">图形均衡器</h2>
                    <div class="bg-card-bg rounded-xl border border-border-color p-6 backdrop-filter backdrop-blur-lg">
                        <div class="flex justify-center items-end">
                            <template x-for="(band, index) in eqBands" :key="index">
                                <div class="eq-band">
                                    <div class="eq-frequency" x-text="band.frequency"></div>
                                    <input type="range" 
                                           min="-12" 
                                           max="12" 
                                           step="0.5"
                                           :value="band.gain"
                                           @input="updateBand(index, $event.target.value)"
                                           class="eq-slider">
                                    <div class="eq-value" x-text="band.gain + 'dB'"></div>
                                </div>
                            </template>
                        </div>
                    </div>
                </div>
                
                <!-- 实时波形 -->
                <div>
                    <h2 class="title-md text-primary mb-4">实时波形</h2>
                    <div class="waveform-container">
                        <div class="flex items-end justify-center h-full">
                            <template x-for="i in 50" :key="i">
                                <div class="waveform-bar" 
                                     :style="`height: ${Math.random() * 80 + 20}%; animation-delay: ${i * 0.05}s`"></div>
                            </template>
                        </div>
                    </div>
                </div>
                
                <!-- 音效控制 -->
                <div>
                    <h2 class="title-md text-primary mb-4">音效增强</h2>
                    <div class="audio-controls">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="control-group">
                                <label class="control-label">低音增强</label>
                                <input type="range" 
                                       min="0" 
                                       max="100" 
                                       :value="audioEffects.bassBoost"
                                       @input="audioEffects.bassBoost = $event.target.value"
                                       class="control-slider">
                                <div class="text-xs text-secondary mt-1" x-text="audioEffects.bassBoost + '%'"></div>
                            </div>
                            
                            <div class="control-group">
                                <label class="control-label">3D环绕</label>
                                <input type="range" 
                                       min="0" 
                                       max="100" 
                                       :value="audioEffects.surround"
                                       @input="audioEffects.surround = $event.target.value"
                                       class="control-slider">
                                <div class="text-xs text-secondary mt-1" x-text="audioEffects.surround + '%'"></div>
                            </div>
                            
                            <div class="control-group">
                                <label class="control-label">音场扩展</label>
                                <input type="range" 
                                       min="0" 
                                       max="100" 
                                       :value="audioEffects.soundstage"
                                       @input="audioEffects.soundstage = $event.target.value"
                                       class="control-slider">
                                <div class="text-xs text-secondary mt-1" x-text="audioEffects.soundstage + '%'"></div>
                            </div>
                            
                            <div class="control-group">
                                <label class="control-label">清晰度</label>
                                <input type="range" 
                                       min="0" 
                                       max="100" 
                                       :value="audioEffects.clarity"
                                       @input="audioEffects.clarity = $event.target.value"
                                       class="control-slider">
                                <div class="text-xs text-secondary mt-1" x-text="audioEffects.clarity + '%'"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function equalizerApp() {
            return {
                currentPreset: null,
                
                presets: [
                    { id: 1, name: '平衡', description: '均衡音效', icon: 'material-symbols:balance', gains: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0] },
                    { id: 2, name: '摇滚', description: '强劲节拍', icon: 'material-symbols:music-note', gains: [3, 2, -1, -2, -1, 1, 3, 4, 4, 4] },
                    { id: 3, name: '流行', description: '人声突出', icon: 'material-symbols:trending-up', gains: [-1, -1, 0, 1, 2, 2, 1, 0, -1, -1] },
                    { id: 4, name: '古典', description: '优雅细腻', icon: 'material-symbols:piano', gains: [0, 0, 0, 0, 0, 0, -2, -2, -2, -3] },
                    { id: 5, name: '爵士', description: '温暖醇厚', icon: 'material-symbols:music-video', gains: [2, 1, 0, 1, -1, -1, 0, 1, 2, 3] },
                    { id: 6, name: '电子', description: '动感节拍', icon: 'material-symbols:graphic-eq', gains: [2, 1, 0, -1, -2, 0, 1, 2, 3, 4] }
                ],
                
                eqBands: [
                    { frequency: '32Hz', gain: 0 },
                    { frequency: '64Hz', gain: 0 },
                    { frequency: '125Hz', gain: 0 },
                    { frequency: '250Hz', gain: 0 },
                    { frequency: '500Hz', gain: 0 },
                    { frequency: '1kHz', gain: 0 },
                    { frequency: '2kHz', gain: 0 },
                    { frequency: '4kHz', gain: 0 },
                    { frequency: '8kHz', gain: 0 },
                    { frequency: '16kHz', gain: 0 }
                ],
                
                audioEffects: {
                    bassBoost: 30,
                    surround: 20,
                    soundstage: 40,
                    clarity: 60
                },
                
                applyPreset(preset) {
                    this.currentPreset = preset;
                    preset.gains.forEach((gain, index) => {
                        if (this.eqBands[index]) {
                            this.eqBands[index].gain = gain;
                        }
                    });
                    console.log('🎛️ 应用预设:', preset.name);
                },
                
                updateBand(index, value) {
                    this.eqBands[index].gain = parseFloat(value);
                    this.currentPreset = null; // 清除预设状态
                    console.log(`🎚️ 调节频段 ${this.eqBands[index].frequency}: ${value}dB`);
                },
                
                resetEqualizer() {
                    this.eqBands.forEach(band => band.gain = 0);
                    this.currentPreset = null;
                    console.log('🔄 重置均衡器');
                },
                
                savePreset() {
                    console.log('💾 保存自定义预设');
                    // 保存当前设置为自定义预设
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('🎛️ 均衡器页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 均衡器');
        });
    </script>
</body>
</html>
