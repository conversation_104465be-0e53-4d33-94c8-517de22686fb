<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>空汀 - 听汀音乐播放器</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">

    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>

    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        /* 空汀状态特殊样式 */
        .empty-harbor {
            background: linear-gradient(135deg, 
                var(--bg-primary) 0%, 
                var(--bg-secondary) 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }
        
        .harbor-illustration {
            width: 280px;
            height: 200px;
            margin: 0 auto;
            position: relative;
        }
        
        .harbor-dock {
            stroke: var(--text-secondary);
            stroke-width: 2;
            fill: none;
            opacity: 0.6;
            animation: float 4s ease-in-out infinite;
        }
        
        .water-ripple {
            stroke: var(--color-primary);
            stroke-width: 1;
            fill: none;
            opacity: 0.3;
            animation: ripple-expand 3s ease-out infinite;
        }
        
        @keyframes ripple-expand {
            0% {
                opacity: 0.3;
                transform: scale(1);
            }
            50% {
                opacity: 0.1;
                transform: scale(1.1);
            }
            100% {
                opacity: 0;
                transform: scale(1.2);
            }
        }
        
        .scan-button {
            background: linear-gradient(135deg, var(--color-accent) 0%, #E6C547 100%);
            box-shadow: 0 8px 32px rgba(212, 175, 55, 0.25);
            transition: all 0.3s ease;
        }
        
        .scan-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(212, 175, 55, 0.35);
        }
        
        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }
        
        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: var(--color-primary);
            border-radius: 50%;
            opacity: 0.2;
            animation: float-particle 6s ease-in-out infinite;
        }
        
        @keyframes float-particle {
            0%, 100% {
                transform: translateY(0px) translateX(0px);
                opacity: 0.2;
            }
            50% {
                transform: translateY(-20px) translateX(10px);
                opacity: 0.4;
            }
        }
    </style>
</head>
<body class="font-primary">
    <!-- 主题管理器 -->
    <script src="../assets/js/theme.js"></script>
    
    <!-- 空汀状态页面 -->
    <div class="empty-harbor water-bg" x-data="emptyState()">
        <!-- 浮动粒子效果 -->
        <div class="floating-particles">
            <div class="particle" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
            <div class="particle" style="top: 40%; left: 80%; animation-delay: 1s;"></div>
            <div class="particle" style="top: 60%; left: 20%; animation-delay: 2s;"></div>
            <div class="particle" style="top: 80%; left: 70%; animation-delay: 3s;"></div>
            <div class="particle" style="top: 30%; left: 50%; animation-delay: 4s;"></div>
        </div>
        
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="header-brand">
                    <iconify-icon icon="material-symbols:anchor" class="header-brand-icon"></iconify-icon>
                    <h1 class="header-brand-text">听汀</h1>
                </div>

                <div class="header-actions">
                    <!-- 主题切换按钮 -->
                    <button
                        data-theme-toggle
                        class="header-theme-toggle"
                        aria-label="切换主题"
                    >
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                        <span class="theme-text text-sm font-medium text-primary">晨汀</span>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 主要内容区域 -->
        <main class="flex flex-col items-center justify-center px-6 py-12 min-h-[calc(100vh-120px)]">
            <!-- 码头插画 -->
            <div class="harbor-illustration mb-12">
                <svg viewBox="0 0 280 200" class="w-full h-full">
                    <!-- 水面 -->
                    <ellipse cx="140" cy="180" rx="120" ry="15" class="water-ripple" style="animation-delay: 0s;"/>
                    <ellipse cx="140" cy="180" rx="100" ry="12" class="water-ripple" style="animation-delay: 1s;"/>
                    <ellipse cx="140" cy="180" rx="80" ry="10" class="water-ripple" style="animation-delay: 2s;"/>
                    
                    <!-- 码头结构 -->
                    <g class="harbor-dock">
                        <!-- 主码头 -->
                        <path d="M60 120 L220 120 L220 140 L60 140 Z" />
                        
                        <!-- 码头桩 -->
                        <line x1="80" y1="120" x2="80" y2="160" />
                        <line x1="120" y1="120" x2="120" y2="160" />
                        <line x1="160" y1="120" x2="160" y2="160" />
                        <line x1="200" y1="120" x2="200" y2="160" />
                        
                        <!-- 栏杆 -->
                        <line x1="60" y1="110" x2="220" y2="110" />
                        <line x1="70" y1="110" x2="70" y2="120" />
                        <line x1="100" y1="110" x2="100" y2="120" />
                        <line x1="140" y1="110" x2="140" y2="120" />
                        <line x1="180" y1="110" x2="180" y2="120" />
                        <line x1="210" y1="110" x2="210" y2="120" />
                    </g>
                    
                    <!-- 远山轮廓 -->
                    <path d="M0 80 Q70 60 140 70 Q210 80 280 65 L280 0 L0 0 Z" 
                          fill="var(--bg-secondary)" opacity="0.3"/>
                </svg>
            </div>
            
            <!-- 文案内容 -->
            <div class="text-center mb-12 max-w-md">
                <h2 class="title-xl text-primary mb-4">空汀</h2>
                <p class="text-lg text-secondary mb-2">请把音乐放进你的汀</p>
                <p class="text-sm text-secondary opacity-75">
                    在这里，每一首歌都是停泊的船<br>
                    每一个收藏都是心灵的锚点
                </p>
            </div>
            
            <!-- 扫描按钮 -->
            <button 
                @click="scanMusic()"
                class="scan-button text-white font-semibold px-8 py-4 rounded-xl flex items-center space-x-3 ripple-effect"
            >
                <iconify-icon icon="material-symbols:folder-open" class="text-xl"></iconify-icon>
                <span>扫描音乐文件</span>
            </button>
            
            <!-- 提示信息 -->
            <div class="mt-8 text-center">
                <p class="text-xs text-secondary opacity-60">
                    支持 MP3、FLAC、WAV、M4A、OGG 格式
                </p>
            </div>
        </main>
        
        <!-- 底部信息 -->
        <footer class="text-center py-6 text-xs text-secondary opacity-50">
            <p>听汀 - 你的私人音乐港湾</p>
        </footer>
    </div>
    
    <script>
        function emptyState() {
            return {
                isScanning: false,
                
                async scanMusic() {
                    if (this.isScanning) return;
                    
                    this.isScanning = true;
                    
                    // 模拟扫描过程
                    try {
                        // 这里会调用实际的文件扫描API
                        console.log('🎵 开始扫描音乐文件...');
                        
                        // 模拟扫描延迟
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        // 扫描完成后跳转到主界面
                        window.location.href = './main.html';
                        
                    } catch (error) {
                        console.error('扫描失败:', error);
                        alert('扫描音乐文件失败，请重试');
                    } finally {
                        this.isScanning = false;
                    }
                },
                
                init() {
                    console.log('🌊 空汀状态页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 空汀状态页面');
        });
    </script>
</body>
</html>
