# 项目提示词历史

## 需求分析提示词

## PRD提示词

## UI UX设计提示词

## 原型视觉规范提示词

## 高保真原型开发提示词

```md
James，请开始高保真原型设计：
1、使用HTML5、Tailwind css、**iconify-icons**、chart.js、Alpine.js和JavaScript，通过cdn`unpkg.com`引入，
2、每个页面一个文件，请正确的引入cnd图标库，不可以使用emoji作为icon;
3、使用ios/ipad设计规范，真实地模拟App体验，所有页面无需边框和顶栏使用响应式布局，组件、页面宽高自适应;
4、完整阅读docs/*下文档，一些特别重要定义规范在**prd**、**ui/ux**、**x_design**中;
5、使用中国风 Neo-Chinese × 极简主义 × 暖色低对比暗色作为设计语言，请确保原型符合预期;
6、品牌视觉等全局样式使用css变量定义在assets/css/styles.css下，所有页面统一引入
7、使用`classMode`模式进行暗色模式适配，将taiwindcss配置定义在assets/js/*下，所有页面统一引入;
8、实现全局的主题管理prototype/assets/js/theme.js完成对亮暗主题切换，所有页面统一引入;
9、在prototype/pages/*目录高保真原型的编码
10、请先制详细的开发计划，再按计划一步一步执行，最后维护更新prototype\{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档

请继续执行开发任务：
1、prototype\pages\empty-state.html页面的版面设计非常契合我们视觉风格，请继续保持，使用该风格对其他页面/组件进行优化;
2、prototype\pages\main.html底部播放控制栏视觉呈现效果不满意，请进行优化;
3、prototype\pages\player.html页面的底部控制区域，请进行优化,
4、但是所有页面顶部导航栏视觉呈现效果不满意，请进行优化，禁止使用emoji图标;
10、请先制详细的开发计划，再按计划一步一步执行，最后维护更新prototype\{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档

请按以下下步骤执行开发任务：
1、完成搜索、我的、音乐库、专辑、艺术家、歌曲详细、播放列表、我的锚点，音乐空间，均衡器页面开发，并为相关页面添加入口;
2、切换到产品经理John，完成步骤1功能需求分析，并更新docs\prd.md;
3、切换到UI/UX设计师Alex，根据步骤1和John更新后prd作为输入，并更新新增页面的视觉设计docs\design.md;
4、切换到全站开发James，根据步骤1、2、3功作为输入完成原型的编码，请确保风与当前项目保持一致;
5、请先制详细的开发计划，再按计划一步一步执行，最后维护更新prototype\{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档

James，请继续执行开发任务：
1、新开发页面prototype\pages\{album|anchors|equalizer|library|profile|search}.html都存在布局问题详看截图，请修复
2、完成修复后继续对未完成的页面进行开发
5、请先制详细的开发计划，再按计划一步一步执行，请确保按顺序执行任务，不可偷懒和怠工，最后维护更新prototype\{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档

James，请继续执行开发任务：
1、完成桌面Widget，注册与登录，数据备份恢复，隐私设置，用户协议，隐私政策，检查更新，联系我们，功能介绍，引导页，帮助反馈开发，并为相关页面添加入口;
2、切换到产品经理John，完成步骤1功能需求分析，并更新docs\prd.md;
3、切换到UI/UX设计师Alex，根据步骤1和John更新后prd作为输入，并更新新增页面的视觉设计docs\design.md;
4、切换到全栈开发James，根据步骤1、2、3功作为输入完成原型的编码，请确保风与当前项目保持一致;
5、请先制详细的开发计划，再按计划一步一步执行，最后维护更新prototype\{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档

James，请继续执行开发任务：
1、完成检查更新，联系我们，功能介绍，引导页的开发;
2、修复pages/{music-space|profile}.html chart.js图表颜色当前项目风格一致，请勿使用css自定义颜色，请使用css变量解决问题;
3、请先制详细的开发计划，再按计划一步一步执行，最后维护更新prototype\{PROJECT_SUMMARY|DEVELOPMENT_PROGRESS|README}.md文档
```

## 架构设计提示词