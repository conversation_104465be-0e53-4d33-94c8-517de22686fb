/**
 * 「听汀」主题管理系统 v1.0
 * 支持晨汀/夜汀主题切换，使用classMode模式
 * 包含主题切换动画、本地存储、系统主题检测
 */

class ThemeManager {
  constructor() {
    this.themes = {
      MORNING: 'theme-morning',
      NIGHT: 'theme-night'
    };
    
    this.currentTheme = this.themes.MORNING;
    this.storageKey = 'tingting-theme';
    this.transitionClass = 'theme-transition';
    
    this.init();
  }
  
  /**
   * 初始化主题管理器
   */
  init() {
    // 添加主题切换过渡效果
    this.addTransitionStyles();
    
    // 从本地存储加载主题
    this.loadThemeFromStorage();
    
    // 监听系统主题变化
    this.watchSystemTheme();
    
    // 应用初始主题
    this.applyTheme(this.currentTheme, false);
    
    // 绑定主题切换事件
    this.bindThemeToggleEvents();
    
    console.log('🌊 听汀主题管理器已初始化');
  }
  
  /**
   * 添加主题切换过渡样式
   */
  addTransitionStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .theme-transition,
      .theme-transition *,
      .theme-transition *:before,
      .theme-transition *:after {
        transition: all 1200ms cubic-bezier(0.4, 0, 0.2, 1) !important;
        transition-delay: 0 !important;
      }
      
      /* 主题切换时的水波纹效果 */
      .theme-switching::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(circle at center, 
          var(--color-primary) 0%, 
          transparent 70%);
        opacity: 0;
        pointer-events: none;
        z-index: 9999;
        animation: themeRipple 1200ms ease-out;
      }
      
      @keyframes themeRipple {
        0% {
          opacity: 0;
          transform: scale(0);
        }
        50% {
          opacity: 0.1;
          transform: scale(1);
        }
        100% {
          opacity: 0;
          transform: scale(1.2);
        }
      }
    `;
    document.head.appendChild(style);
  }
  
  /**
   * 从本地存储加载主题
   */
  loadThemeFromStorage() {
    const savedTheme = localStorage.getItem(this.storageKey);
    if (savedTheme && Object.values(this.themes).includes(savedTheme)) {
      this.currentTheme = savedTheme;
    } else {
      // 如果没有保存的主题，根据系统主题设置默认值
      this.currentTheme = this.getSystemTheme();
    }
  }
  
  /**
   * 获取系统主题偏好
   */
  getSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return this.themes.NIGHT;
    }
    return this.themes.MORNING;
  }
  
  /**
   * 监听系统主题变化
   */
  watchSystemTheme() {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', (e) => {
        // 只有在用户没有手动设置主题时才跟随系统
        if (!localStorage.getItem(this.storageKey)) {
          const systemTheme = e.matches ? this.themes.NIGHT : this.themes.MORNING;
          this.setTheme(systemTheme);
        }
      });
    }
  }
  
  /**
   * 应用主题
   * @param {string} theme - 主题类名
   * @param {boolean} animate - 是否显示切换动画
   */
  applyTheme(theme, animate = true) {
    const html = document.documentElement;
    const body = document.body;
    
    if (animate) {
      // 添加过渡效果
      html.classList.add(this.transitionClass);
      body.classList.add('theme-switching');
      
      // 移除旧主题
      Object.values(this.themes).forEach(t => {
        html.classList.remove(t);
      });
      
      // 应用新主题
      html.classList.add(theme);
      
      // 移除过渡效果和动画类
      setTimeout(() => {
        html.classList.remove(this.transitionClass);
        body.classList.remove('theme-switching');
      }, 1200);
    } else {
      // 直接应用主题，无动画
      Object.values(this.themes).forEach(t => {
        html.classList.remove(t);
      });
      html.classList.add(theme);
    }
    
    this.currentTheme = theme;
    
    // 更新主题切换按钮状态
    this.updateThemeToggleButtons();
    
    // 触发主题变化事件
    this.dispatchThemeChangeEvent(theme);
  }
  
  /**
   * 设置主题并保存到本地存储
   * @param {string} theme - 主题类名
   */
  setTheme(theme) {
    if (!Object.values(this.themes).includes(theme)) {
      console.warn(`未知主题: ${theme}`);
      return;
    }
    
    this.applyTheme(theme);
    localStorage.setItem(this.storageKey, theme);
    
    console.log(`🌊 主题已切换至: ${theme === this.themes.MORNING ? '晨汀' : '夜汀'}`);
  }
  
  /**
   * 切换主题
   */
  toggleTheme() {
    const newTheme = this.currentTheme === this.themes.MORNING 
      ? this.themes.NIGHT 
      : this.themes.MORNING;
    
    this.setTheme(newTheme);
  }
  
  /**
   * 获取当前主题
   */
  getCurrentTheme() {
    return this.currentTheme;
  }
  
  /**
   * 检查是否为夜间主题
   */
  isNightTheme() {
    return this.currentTheme === this.themes.NIGHT;
  }
  
  /**
   * 检查是否为晨间主题
   */
  isMorningTheme() {
    return this.currentTheme === this.themes.MORNING;
  }
  
  /**
   * 绑定主题切换按钮事件
   */
  bindThemeToggleEvents() {
    // 为所有带有 data-theme-toggle 属性的元素绑定点击事件
    document.addEventListener('click', (e) => {
      if (e.target.closest('[data-theme-toggle]')) {
        e.preventDefault();
        this.toggleTheme();
      }
    });
    
    // 支持键盘快捷键 Ctrl/Cmd + Shift + T
    document.addEventListener('keydown', (e) => {
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
        e.preventDefault();
        this.toggleTheme();
      }
    });
  }
  
  /**
   * 更新主题切换按钮状态
   */
  updateThemeToggleButtons() {
    const toggleButtons = document.querySelectorAll('[data-theme-toggle]');
    toggleButtons.forEach(button => {
      const icon = button.querySelector('.theme-icon');
      const text = button.querySelector('.theme-text');

      if (this.isNightTheme()) {
        if (icon) {
          if (icon.tagName === 'ICONIFY-ICON') {
            icon.setAttribute('icon', 'material-symbols:dark-mode');
          } else {
            icon.textContent = '🌙';
          }
        }
        if (text) text.textContent = '夜汀';
        button.setAttribute('aria-label', '切换到晨汀主题');
      } else {
        if (icon) {
          if (icon.tagName === 'ICONIFY-ICON') {
            icon.setAttribute('icon', 'material-symbols:light-mode');
          } else {
            icon.textContent = '☀️';
          }
        }
        if (text) text.textContent = '晨汀';
        button.setAttribute('aria-label', '切换到夜汀主题');
      }
    });
  }
  
  /**
   * 触发主题变化事件
   */
  dispatchThemeChangeEvent(theme) {
    const event = new CustomEvent('themechange', {
      detail: {
        theme: theme,
        isNight: theme === this.themes.NIGHT,
        isMorning: theme === this.themes.MORNING
      }
    });
    document.dispatchEvent(event);
  }
  
  /**
   * 重置主题到系统默认
   */
  resetToSystemTheme() {
    localStorage.removeItem(this.storageKey);
    const systemTheme = this.getSystemTheme();
    this.setTheme(systemTheme);
    console.log('🌊 主题已重置为系统默认');
  }
}

// 创建全局主题管理器实例
window.themeManager = new ThemeManager();

// 导出主题管理器（用于模块化环境）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = ThemeManager;
}

// 为 Alpine.js 提供主题管理功能
if (window.Alpine) {
  window.Alpine.data('themeManager', () => ({
    currentTheme: window.themeManager.getCurrentTheme(),
    isNight: window.themeManager.isNightTheme(),
    
    toggle() {
      window.themeManager.toggleTheme();
      this.currentTheme = window.themeManager.getCurrentTheme();
      this.isNight = window.themeManager.isNightTheme();
    },
    
    setTheme(theme) {
      window.themeManager.setTheme(theme);
      this.currentTheme = window.themeManager.getCurrentTheme();
      this.isNight = window.themeManager.isNightTheme();
    }
  }));
}

console.log('🌊 听汀主题管理系统加载完成');
