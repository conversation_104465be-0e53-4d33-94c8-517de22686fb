<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>欢迎使用听汀 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .onboarding-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        .onboarding-card {
            background: var(--card-bg);
            border-radius: 32px;
            padding: 3rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(20px);
            width: 100%;
            max-width: 500px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .onboarding-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 6px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .slide {
            text-align: center;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.5s ease;
        }
        
        .slide.active {
            opacity: 1;
            transform: translateX(0);
        }
        
        .slide-icon {
            width: 120px;
            height: 120px;
            border-radius: 30px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            margin: 0 auto 2rem;
            position: relative;
            animation: float 3s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .slide-icon::after {
            content: '';
            position: absolute;
            inset: -4px;
            border-radius: 34px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            z-index: -1;
            opacity: 0.3;
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.3; }
            50% { transform: scale(1.05); opacity: 0.5; }
        }
        
        .slide-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }
        
        .slide-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 2rem;
            font-size: 1.125rem;
        }
        
        .slide-features {
            list-style: none;
            padding: 0;
            margin: 2rem 0;
            text-align: left;
        }
        
        .slide-features li {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 1rem;
            color: var(--text-secondary);
            padding: 0.75rem;
            border-radius: 12px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
        }
        
        .feature-icon {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }
        
        .progress-indicators {
            display: flex;
            justify-content: center;
            gap: 0.5rem;
            margin: 2rem 0;
        }
        
        .progress-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .progress-dot.active {
            background: var(--color-primary);
            transform: scale(1.2);
        }
        
        .navigation-buttons {
            display: flex;
            justify-content: space-between;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .nav-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            flex: 1;
            justify-content: center;
        }
        
        .nav-button.primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
        }
        
        .nav-button.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
        }
        
        .nav-button.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .nav-button.secondary:hover {
            background: var(--card-bg);
            transform: translateY(-2px);
        }
        
        .nav-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }
        
        .skip-button {
            position: absolute;
            top: 2rem;
            right: 2rem;
            background: var(--bg-secondary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .skip-button:hover {
            background: var(--card-bg);
            color: var(--text-primary);
        }
        
        .welcome-animation {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-note {
            position: absolute;
            color: var(--color-primary);
            opacity: 0.1;
            animation: floatNote 8s ease-in-out infinite;
        }
        
        @keyframes floatNote {
            0%, 100% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10%, 90% { opacity: 0.1; }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .floating-note:nth-child(1) { left: 10%; animation-delay: 0s; font-size: 2rem; }
        .floating-note:nth-child(2) { left: 30%; animation-delay: 2s; font-size: 1.5rem; }
        .floating-note:nth-child(3) { left: 50%; animation-delay: 4s; font-size: 2.5rem; }
        .floating-note:nth-child(4) { left: 70%; animation-delay: 6s; font-size: 1.8rem; }
        .floating-note:nth-child(5) { left: 90%; animation-delay: 1s; font-size: 2.2rem; }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="onboarding-container water-bg" x-data="onboardingApp()">
        <!-- 背景动画 -->
        <div class="welcome-animation">
            <iconify-icon icon="material-symbols:music-note" class="floating-note"></iconify-icon>
            <iconify-icon icon="material-symbols:music-note" class="floating-note"></iconify-icon>
            <iconify-icon icon="material-symbols:music-note" class="floating-note"></iconify-icon>
            <iconify-icon icon="material-symbols:music-note" class="floating-note"></iconify-icon>
            <iconify-icon icon="material-symbols:music-note" class="floating-note"></iconify-icon>
        </div>
        
        <!-- 跳过按钮 -->
        <button @click="skipOnboarding()" class="skip-button">
            跳过引导
        </button>
        
        <!-- 引导卡片 -->
        <div class="onboarding-card">
            <!-- 幻灯片内容 -->
            <div class="relative">
                <!-- 欢迎页面 -->
                <div class="slide" :class="currentSlide === 0 ? 'active' : ''" x-show="currentSlide === 0">
                    <div class="slide-icon">
                        <iconify-icon icon="material-symbols:waving-hand"></iconify-icon>
                    </div>
                    <h1 class="slide-title">欢迎来到听汀</h1>
                    <p class="slide-description">
                        水边的静谧，音乐的停泊<br>
                        一款专注于纯粹音乐体验的播放器
                    </p>
                </div>
                
                <!-- 功能介绍1 -->
                <div class="slide" :class="currentSlide === 1 ? 'active' : ''" x-show="currentSlide === 1">
                    <div class="slide-icon">
                        <iconify-icon icon="material-symbols:music-note"></iconify-icon>
                    </div>
                    <h2 class="slide-title">纯粹音乐体验</h2>
                    <p class="slide-description">无广告、无推送、无网络依赖，只有音乐和你</p>
                    <ul class="slide-features">
                        <li>
                            <div class="feature-icon">
                                <iconify-icon icon="material-symbols:high-quality" class="text-sm"></iconify-icon>
                            </div>
                            支持多种高品质音频格式
                        </li>
                        <li>
                            <div class="feature-icon">
                                <iconify-icon icon="material-symbols:offline-bolt" class="text-sm"></iconify-icon>
                            </div>
                            完全本地播放，无需网络
                        </li>
                        <li>
                            <div class="feature-icon">
                                <iconify-icon icon="material-symbols:block" class="text-sm"></iconify-icon>
                            </div>
                            零广告干扰，纯净体验
                        </li>
                    </ul>
                </div>
                
                <!-- 功能介绍2 -->
                <div class="slide" :class="currentSlide === 2 ? 'active' : ''" x-show="currentSlide === 2">
                    <div class="slide-icon">
                        <iconify-icon icon="material-symbols:anchor"></iconify-icon>
                    </div>
                    <h2 class="slide-title">情感化收藏</h2>
                    <p class="slide-description">独创的"锚点"收藏系统，让每一首歌都成为情感的停泊</p>
                    <ul class="slide-features">
                        <li>
                            <div class="feature-icon">
                                <iconify-icon icon="material-symbols:favorite" class="text-sm"></iconify-icon>
                            </div>
                            情感标签分类收藏
                        </li>
                        <li>
                            <div class="feature-icon">
                                <iconify-icon icon="material-symbols:timeline" class="text-sm"></iconify-icon>
                            </div>
                            时间轴记录音乐足迹
                        </li>
                        <li>
                            <div class="feature-icon">
                                <iconify-icon icon="material-symbols:palette" class="text-sm"></iconify-icon>
                            </div>
                            心情色彩标记系统
                        </li>
                    </ul>
                </div>
                
                <!-- 功能介绍3 -->
                <div class="slide" :class="currentSlide === 3 ? 'active' : ''" x-show="currentSlide === 3">
                    <div class="slide-icon">
                        <iconify-icon icon="material-symbols:auto-awesome"></iconify-icon>
                    </div>
                    <h2 class="slide-title">中国风设计</h2>
                    <p class="slide-description">融合传统美学与现代设计，打造独特的视觉体验</p>
                    <ul class="slide-features">
                        <li>
                            <div class="feature-icon">
                                <iconify-icon icon="material-symbols:water-drop" class="text-sm"></iconify-icon>
                            </div>
                            水波纹交互反馈
                        </li>
                        <li>
                            <div class="feature-icon">
                                <iconify-icon icon="material-symbols:blur-on" class="text-sm"></iconify-icon>
                            </div>
                            毛玻璃质感界面
                        </li>
                        <li>
                            <div class="feature-icon">
                                <iconify-icon icon="material-symbols:wb-sunny" class="text-sm"></iconify-icon>
                            </div>
                            晨汀/夜汀主题切换
                        </li>
                    </ul>
                </div>
                
                <!-- 开始使用 -->
                <div class="slide" :class="currentSlide === 4 ? 'active' : ''" x-show="currentSlide === 4">
                    <div class="slide-icon">
                        <iconify-icon icon="material-symbols:rocket-launch"></iconify-icon>
                    </div>
                    <h2 class="slide-title">开始你的音乐之旅</h2>
                    <p class="slide-description">
                        一切准备就绪！<br>
                        让我们一起探索听汀的美妙世界
                    </p>
                </div>
            </div>
            
            <!-- 进度指示器 -->
            <div class="progress-indicators">
                <template x-for="(slide, index) in slides" :key="index">
                    <div @click="goToSlide(index)" 
                         class="progress-dot"
                         :class="currentSlide === index ? 'active' : ''">
                    </div>
                </template>
            </div>
            
            <!-- 导航按钮 -->
            <div class="navigation-buttons">
                <button @click="previousSlide()" 
                        class="nav-button secondary"
                        :disabled="currentSlide === 0"
                        x-show="currentSlide > 0">
                    <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    上一步
                </button>
                
                <button @click="nextSlide()" 
                        class="nav-button primary"
                        x-show="currentSlide < slides.length - 1">
                    下一步
                    <iconify-icon icon="material-symbols:arrow-forward" class="text-lg"></iconify-icon>
                </button>
                
                <button @click="startApp()" 
                        class="nav-button primary"
                        x-show="currentSlide === slides.length - 1">
                    开始使用
                    <iconify-icon icon="material-symbols:play-arrow" class="text-lg"></iconify-icon>
                </button>
            </div>
        </div>
    </div>
    
    <script>
        function onboardingApp() {
            return {
                currentSlide: 0,
                slides: [
                    { title: '欢迎', icon: 'waving-hand' },
                    { title: '纯粹体验', icon: 'music-note' },
                    { title: '情感收藏', icon: 'anchor' },
                    { title: '中国风设计', icon: 'auto-awesome' },
                    { title: '开始使用', icon: 'rocket-launch' }
                ],
                
                nextSlide() {
                    if (this.currentSlide < this.slides.length - 1) {
                        this.currentSlide++;
                        console.log('📱 下一步:', this.slides[this.currentSlide].title);
                    }
                },
                
                previousSlide() {
                    if (this.currentSlide > 0) {
                        this.currentSlide--;
                        console.log('📱 上一步:', this.slides[this.currentSlide].title);
                    }
                },
                
                goToSlide(index) {
                    this.currentSlide = index;
                    console.log('📱 跳转到:', this.slides[index].title);
                },
                
                startApp() {
                    console.log('🚀 开始使用听汀');
                    // 保存引导完成状态
                    localStorage.setItem('onboarding_completed', 'true');
                    // 跳转到主界面
                    window.location.href = './main.html';
                },
                
                skipOnboarding() {
                    console.log('⏭️ 跳过引导');
                    if (confirm('确定要跳过引导吗？你可以稍后在帮助页面查看功能介绍。')) {
                        localStorage.setItem('onboarding_completed', 'true');
                        window.location.href = './main.html';
                    }
                },
                
                init() {
                    console.log('👋 引导页面已加载');
                    
                    // 检查是否已完成引导
                    if (localStorage.getItem('onboarding_completed') === 'true') {
                        console.log('引导已完成，跳转到主界面');
                        window.location.href = './main.html';
                        return;
                    }
                    
                    // 键盘导航
                    document.addEventListener('keydown', (e) => {
                        if (e.key === 'ArrowRight' || e.key === ' ') {
                            e.preventDefault();
                            this.nextSlide();
                        } else if (e.key === 'ArrowLeft') {
                            e.preventDefault();
                            this.previousSlide();
                        } else if (e.key === 'Escape') {
                            this.skipOnboarding();
                        }
                    });
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 欢迎引导');
        });
    </script>
</body>
</html>
