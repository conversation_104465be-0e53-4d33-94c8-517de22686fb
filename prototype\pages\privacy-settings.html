<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>隐私设置 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .privacy-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .privacy-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .privacy-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .setting-item {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            padding: 1.5rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .setting-info {
            flex: 1;
            margin-right: 1rem;
        }
        
        .setting-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .setting-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.5;
        }
        
        .toggle-switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: var(--bg-secondary);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
            flex-shrink: 0;
        }
        
        .toggle-switch.active {
            background: var(--color-primary);
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(24px);
        }
        
        .privacy-level {
            display: flex;
            gap: 1rem;
            margin-top: 1rem;
        }
        
        .level-option {
            flex: 1;
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-secondary);
        }
        
        .level-option.active {
            border-color: var(--color-primary);
            background: var(--card-bg);
        }
        
        .level-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .level-description {
            font-size: 0.75rem;
            color: var(--text-secondary);
        }
        
        .danger-zone {
            border: 2px solid #ef4444;
            border-radius: 16px;
            padding: 1.5rem;
            background: rgba(239, 68, 68, 0.05);
        }
        
        .danger-title {
            color: #ef4444;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .danger-button {
            background: #ef4444;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .danger-button:hover {
            background: #dc2626;
            transform: translateY(-2px);
        }
        
        .save-button {
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-top: 2rem;
        }
        
        .save-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="privacy-container water-bg" x-data="privacyApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">隐私设置</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="resetToDefault()" class="header-button" aria-label="重置">
                        <iconify-icon icon="material-symbols:restart-alt" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 隐私设置内容 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 隐私级别 -->
                <div class="privacy-card">
                    <h2 class="text-xl font-semibold text-primary mb-4">隐私保护级别</h2>
                    <p class="text-secondary mb-6">选择适合你的隐私保护级别</p>
                    
                    <div class="privacy-level">
                        <div class="level-option" :class="privacyLevel === 'low' ? 'active' : ''" @click="privacyLevel = 'low'">
                            <div class="level-title">基础</div>
                            <div class="level-description">基本隐私保护</div>
                        </div>
                        <div class="level-option" :class="privacyLevel === 'medium' ? 'active' : ''" @click="privacyLevel = 'medium'">
                            <div class="level-title">标准</div>
                            <div class="level-description">平衡功能与隐私</div>
                        </div>
                        <div class="level-option" :class="privacyLevel === 'high' ? 'active' : ''" @click="privacyLevel = 'high'">
                            <div class="level-title">严格</div>
                            <div class="level-description">最大隐私保护</div>
                        </div>
                    </div>
                </div>
                
                <!-- 数据收集设置 -->
                <div class="privacy-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">数据收集设置</h2>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">使用统计</div>
                            <div class="setting-description">收集应用使用统计信息，帮助改进产品体验</div>
                        </div>
                        <div class="toggle-switch" :class="settings.analytics ? 'active' : ''" @click="settings.analytics = !settings.analytics">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">崩溃报告</div>
                            <div class="setting-description">自动发送崩溃报告，帮助修复问题</div>
                        </div>
                        <div class="toggle-switch" :class="settings.crashReports ? 'active' : ''" @click="settings.crashReports = !settings.crashReports">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">个性化推荐</div>
                            <div class="setting-description">基于听歌习惯提供个性化音乐推荐</div>
                        </div>
                        <div class="toggle-switch" :class="settings.personalization ? 'active' : ''" @click="settings.personalization = !settings.personalization">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 第三方数据共享 -->
                <div class="privacy-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">第三方数据共享</h2>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">社交媒体分享</div>
                            <div class="setting-description">允许分享音乐到社交媒体平台</div>
                        </div>
                        <div class="toggle-switch" :class="settings.socialSharing ? 'active' : ''" @click="settings.socialSharing = !settings.socialSharing">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">音乐服务集成</div>
                            <div class="setting-description">与其他音乐服务同步播放列表</div>
                        </div>
                        <div class="toggle-switch" :class="settings.musicIntegration ? 'active' : ''" @click="settings.musicIntegration = !settings.musicIntegration">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 个人信息管理 -->
                <div class="privacy-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">个人信息管理</h2>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">显示真实姓名</div>
                            <div class="setting-description">在个人资料中显示真实姓名</div>
                        </div>
                        <div class="toggle-switch" :class="settings.showRealName ? 'active' : ''" @click="settings.showRealName = !settings.showRealName">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">公开播放列表</div>
                            <div class="setting-description">允许其他用户查看你的播放列表</div>
                        </div>
                        <div class="toggle-switch" :class="settings.publicPlaylists ? 'active' : ''" @click="settings.publicPlaylists = !settings.publicPlaylists">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="setting-item">
                        <div class="setting-info">
                            <div class="setting-title">听歌历史</div>
                            <div class="setting-description">保存和显示听歌历史记录</div>
                        </div>
                        <div class="toggle-switch" :class="settings.listeningHistory ? 'active' : ''" @click="settings.listeningHistory = !settings.listeningHistory">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 危险操作区域 -->
                <div class="privacy-card">
                    <div class="danger-zone">
                        <div class="danger-title">
                            <iconify-icon icon="material-symbols:warning" class="text-lg"></iconify-icon>
                            危险操作
                        </div>
                        <p class="text-secondary mb-4">以下操作不可逆，请谨慎操作</p>
                        
                        <div class="flex gap-3">
                            <button @click="clearAllData()" class="danger-button">
                                清除所有数据
                            </button>
                            <button @click="deleteAccount()" class="danger-button">
                                删除账户
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 保存按钮 -->
                <button @click="saveSettings()" class="save-button">
                    <iconify-icon icon="material-symbols:save" class="mr-2"></iconify-icon>
                    保存设置
                </button>
            </div>
        </div>
    </div>
    
    <script>
        function privacyApp() {
            return {
                privacyLevel: 'medium',
                
                settings: {
                    analytics: true,
                    crashReports: true,
                    personalization: true,
                    socialSharing: false,
                    musicIntegration: true,
                    showRealName: false,
                    publicPlaylists: false,
                    listeningHistory: true
                },
                
                saveSettings() {
                    console.log('💾 保存隐私设置', {
                        level: this.privacyLevel,
                        settings: this.settings
                    });
                    
                    // 显示保存成功提示
                    alert('隐私设置已保存');
                },
                
                resetToDefault() {
                    if (confirm('确定要重置为默认设置吗？')) {
                        this.privacyLevel = 'medium';
                        this.settings = {
                            analytics: true,
                            crashReports: true,
                            personalization: true,
                            socialSharing: false,
                            musicIntegration: true,
                            showRealName: false,
                            publicPlaylists: false,
                            listeningHistory: true
                        };
                        console.log('🔄 重置为默认设置');
                    }
                },
                
                clearAllData() {
                    if (confirm('确定要清除所有数据吗？此操作不可恢复！')) {
                        console.log('🗑️ 清除所有数据');
                        // 实现清除数据逻辑
                    }
                },
                
                deleteAccount() {
                    if (confirm('确定要删除账户吗？此操作不可恢复！')) {
                        console.log('❌ 删除账户');
                        // 实现删除账户逻辑
                    }
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('🔒 隐私设置页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 隐私设置');
        });
    </script>
</body>
</html>
