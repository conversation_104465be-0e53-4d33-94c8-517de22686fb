# HarmonyOS开发 - LLMs的ArkTS项目最佳实践规范

## 概述

本文档为大语言模型(LLMs)提供HarmonyOS应用开发的标准化指导，基于ArkTS语言规范和HarmonyOS代码工坊项目的实际架构。重点关注TypeScript到ArkTS的适配规则，确保AI生成的代码符合ArkTS规范要求。

**文档目标**：
- 为LLMs提供完整的ArkTS语法约束指导
- 确保生成的代码符合ArkTS编译要求
- 提供实用的代码模式和最佳实践
- 建立统一的代码质量标准

**适用范围**：
- 所有基于ArkTS的HarmonyOS应用开发
- LLMs辅助的代码生成和重构
- 代码审查和质量检查

## 核心架构原则

### 1. 严格分层架构 (CRITICAL)

**必须遵循的三层架构**:
```
Products层 (HAP包) → Features层 (HAR包) → Common层 (HAR包)
```

**依赖规则**:
- ✅ Products可以依赖Features和Common
- ✅ Features可以依赖Common和其他Features
- ❌ 绝对禁止反向依赖
- ❌ Common层不能依赖任何业务层

**实际应用**:
```typescript
// ✅ 正确: Products层引用Features
import { ComponentLibraryView } from '@ohos/componentlibrary';

// ✅ 正确: Features层引用Common
import { Logger, CommonConstants } from '@ohos/common';

// ❌ 错误: Common层引用Features
import { SampleCard } from '@ohos/devpractices'; // 禁止!
```

### 2. 模块包类型规范

**HAP包 (应用入口)**:
- 位置: `products/` 目录
- 用途: 设备特定的应用入口
- 配置: `"type": "entry"` in module.json5

**HAR包 (静态库)**:
- 位置: `features/` 和 `common/` 目录
- 用途: 可复用的业务模块和公共能力
- 配置: `"type": "har"` in module.json5

**设备类型配置**:
```json5
// 手机/平板/PC共包
"deviceTypes": ["phone", "tablet", "2in1"]
```

## ArkTS语法约束指导 (CRITICAL)

### 1. 强制静态类型约束

**禁止使用any和unknown类型**:
```typescript
// ❌ 错误: 使用any类型
let res: any = some_api_function('hello', 'world');

// ❌ 错误: 使用unknown类型  
let value: unknown = getData();

// ✅ 正确: 使用具体类型
class CallResult {
  public succeeded(): boolean { return true; }
  public errorMessage(): string { return ''; }
}
let res: CallResult = some_api_function('hello', 'world');

// ✅ 正确: 使用Object作为通用类型
let value_o1: Object = true;
let value_o2: Object = 42;
```

**变量声明约束**:
```typescript
// ❌ 错误: 使用var关键字
var x = 'hello';

// ✅ 正确: 使用let声明变量
let x: string = 'hello';

// ✅ 正确: 使用const声明常量
const PI: number = 3.14159;
```

### 2. 对象和属性约束

**对象属性名必须是合法标识符**:
```typescript
// ❌ 错误: 数字或字符串作为属性名
var x = { name: 'x', 2: '3' };
console.info(x['name']);
console.info(x[2]);

// ✅ 正确: 使用类和数组
class X {
  public name: string = '';
}
let x: X = { name: 'x' };
console.info(x.name);

let y = ['a', 'b', 'c'];
console.info(y[2]);

// ✅ 正确: 使用Map处理非标识符key
let z = new Map<Object, string>();
z.set('name', '1');
z.set(2, '2');
```

**禁止运行时修改对象布局**:
```typescript
// ❌ 错误: 动态添加属性
class Point {
  public x: number = 0;
  public y: number = 0;
}
let p = new Point();
(p as any).z = 'Label'; // 编译错误

// ✅ 正确: 在类中预定义所有属性
class Point3D {
  public x: number = 0;
  public y: number = 0;
  public z: number = 0;
}
```

### 3. 函数和方法约束

**函数返回类型标注**:
```typescript
// ❌ 错误: 省略返回类型可能导致推断失败
function f(x: number) {
  if (x <= 0) {
    return x;
  }
  return g(x); // 如果g的返回类型未标注，会报错
}

// ✅ 正确: 显式标注返回类型
function f(x: number): number {
  if (x <= 0) {
    return x;
  }
  return g(x);
}

function g(x: number): number {
  return f(x - 1);
}
```

**禁止函数内声明函数**:
```typescript
// ❌ 错误: 函数内声明函数
function addNum(a: number, b: number): void {
  function logToConsole(message: string): void {
    console.info(message);
  }
  logToConsole('result');
}

// ✅ 正确: 使用lambda函数
function addNum(a: number, b: number): void {
  let logToConsole: (message: string) => void = (message: string): void => {
    console.info(message);
  };
  logToConsole('result');
}
```

### 4. 类型转换和断言

**使用as进行类型转换**:
```typescript
// ❌ 错误: 使用<type>语法
let c1 = <Circle>createShape();

// ✅ 正确: 使用as语法
let c2 = createShape() as Circle;

// ✅ 正确: 创建引用类型对象
let e2 = new Number(5.0) instanceof Number; // true
```

**限制一元运算符使用**:
```typescript
// ❌ 错误: 对字符串使用一元运算符
let b = +'5';    // 编译错误
let d = -'5';    // 编译错误
let f = ~'5';    // 编译错误

// ✅ 正确: 仅对数值类型使用
let a = +5;      // 正确
let c = -5;      // 正确
let e = ~5;      // 正确

// ✅ 正确: 显式类型转换
let b = Number.parseInt('5');
let d = -Number.parseInt('5');
```

### 5. 模块和导入约束

**导入语句位置**:
```typescript
// ❌ 错误: import语句不在文件开头
class C {
  s: string = '';
}
import foo from 'module1';

// ✅ 正确: import语句在文件开头
import foo from 'module1';
import * as bar from 'module2';

class C {
  s: string = '';
}
```

**禁止特定导入语法**:
```typescript
// ❌ 错误: 使用require语法
import m = require('mod');

// ❌ 错误: 使用export =语法
export = Point;

// ✅ 正确: 使用标准ES6导入导出
import * as m from 'mod';
export class Point { }
export default Point;
```

### 6. 接口和类约束

**接口继承限制**:
```typescript
// ❌ 错误: 接口继承类
class Control {
  state: number = 0;
}
interface SelectableControl extends Control {
  select(): void;
}

// ✅ 正确: 接口继承接口
interface Control {
  state: number;
}
interface SelectableControl extends Control {
  select(): void;
}
```

**类实现约束**:
```typescript
// ❌ 错误: 类implements类
class C {
  foo() {}
}
class C1 implements C {
  foo() {}
}

// ✅ 正确: 类implements接口
interface C {
  foo(): void;
}
class C1 implements C {
  foo() {}
}
```

## 类型系统最佳实践

### 1. 严格类型定义

**基础类型使用**:
```typescript
// ✅ 推荐的基础类型定义
let name: string = 'HarmonyOS';
let version: number = 4.0;
let isEnabled: boolean = true;
let items: string[] = ['item1', 'item2'];
let callback: (data: string) => void = (data) => console.info(data);
```

**对象类型定义**:
```typescript
// ❌ 错误: 使用对象字面量类型
let point: { x: number; y: number } = { x: 1, y: 2 };

// ✅ 正确: 使用类或接口定义
interface Point {
  x: number;
  y: number;
}
// 或者
class Point {
  x: number = 0;
  y: number = 0;
}
let point: Point = { x: 1, y: 2 };
```

### 2. 泛型使用规范

**泛型函数类型实参**:
```typescript
// ❌ 错误: 省略泛型类型实参可能导致推断失败
function greet<T>(): T {
  return 'Hello' as T;
}
let z = greet(); // 类型推断为unknown

// ✅ 正确: 显式提供泛型类型实参
let z = greet<string>();

// ✅ 正确: 可从参数推断的情况
function choose<T>(x: T, y: T): T {
  return Math.random() < 0.5 ? x : y;
}
let x = choose(10, 20); // 可以推断为number
```

**泛型约束**:
```typescript
// ❌ 错误: 使用条件类型
type X<T> = T extends number ? T : never;

// ✅ 正确: 使用显式约束
type X1<T extends number> = T;

// ✅ 正确: 使用Object重写
type X2<T> = Object;
```

### 3. 联合类型和可选属性

**联合类型使用**:
```typescript
// ✅ 正确: 基础联合类型
type Status = 'loading' | 'success' | 'error';
let currentStatus: Status = 'loading';

// ✅ 正确: 包含null的联合类型
let value: string | null = null;
let count: number | undefined = undefined;
```

**可选属性定义**:
```typescript
// ✅ 正确: 接口中的可选属性
interface UserConfig {
  name: string;
  age?: number;
  email?: string;
}

// ✅ 正确: 类中的可选属性处理
class User {
  name: string;
  age: number | undefined;
  email: string | undefined;
  
  constructor(config: UserConfig) {
    this.name = config.name;
    this.age = config.age;
    this.email = config.email;
  }
}
```

## 对象和类设计规范

### 1. 类定义最佳实践

**类字段声明**:
```typescript
// ❌ 错误: 在构造函数中声明字段
class Person {
  constructor(protected ssn: string, private firstName: string, private lastName: string) {
    this.ssn = ssn;
    this.firstName = firstName;
    this.lastName = lastName;
  }
}

// ✅ 正确: 在类作用域内显式声明字段
class Person {
  protected ssn: string;
  private firstName: string;
  private lastName: string;

  constructor(ssn: string, firstName: string, lastName: string) {
    this.ssn = ssn;
    this.firstName = firstName;
    this.lastName = lastName;
  }

  getFullName(): string {
    return this.firstName + ' ' + this.lastName;
  }
}
```

**私有字段使用**:
```typescript
// ❌ 错误: 使用#开头的私有字段
class C {
  #foo: number = 42;
}

// ✅ 正确: 使用private关键字
class C {
  private foo: number = 42;
}
```

### 2. 对象字面量使用规范

**对象字面量类型标注**:
```typescript
// ❌ 错误: 未标注类型的对象字面量
let o1 = { n: 42, s: 'foo' };
let o2: Object = { n: 42, s: 'foo' };

// ✅ 正确: 显式类型标注
class C1 {
  n: number = 0;
  s: string = '';
}
let o1: C1 = { n: 42, s: 'foo' };
let o2: C1 = { n: 42, s: 'foo' };
```

### 3. 继承和接口设计

**接口继承规范**:
```typescript
// ✅ 正确: 接口继承接口
interface Identity {
  id: number;
  name: string;
}

interface Contact {
  email: string;
  phoneNumber: string;
}

interface Employee extends Identity, Contact {}
```

**避免structural typing**:
```typescript
// ❌ 错误: 依赖structural typing
class X {
  n: number = 0;
  s: string = '';
}

class Y {
  n: number = 0;
  s: string = '';
}

let x = new X();
let y = new Y();
y = x; // ArkTS中不允许

// ✅ 正确: 使用接口建立关系
interface Z {
  n: number;
  s: string;
}

class X implements Z {
  n: number = 0;
  s: string = '';
}

class Y implements Z {
  n: number = 0;
  s: string = '';
}

let x: Z = new X();
let y: Z = new Y();
y = x; // 合法：相同接口类型
```

## 函数和方法编写规范

### 1. 函数定义约束

**函数表达式限制**:
```typescript
// ❌ 错误: 使用函数表达式
let f = function (s: string) {
  console.info(s);
};

// ✅ 正确: 使用箭头函数
let f = (s: string) => {
  console.info(s);
};

// ✅ 正确: 使用函数声明
function processData(s: string): void {
  console.info(s);
}
```

**异常抛出限制**:
```typescript
// ❌ 错误: 抛出非Error类型
throw 4;
throw '';
throw { message: 'error' };

// ✅ 正确: 抛出Error类或其派生类
throw new Error('Something went wrong');
throw new TypeError('Invalid type');

// ✅ 正确: 自定义错误类
class CustomError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'CustomError';
  }
}
throw new CustomError('Custom error occurred');
```

### 2. 异步函数和Promise

**异步函数定义**:
```typescript
// ✅ 正确: 异步函数返回类型标注
async function fetchData(): Promise<string> {
  try {
    const response = await fetch('/api/data');
    return await response.text();
  } catch (error) {
    throw new Error(`Failed to fetch data: ${error}`);
  }
}

// ✅ 正确: 异步方法在类中的使用
class DataService {
  async loadUserData(userId: number): Promise<UserData> {
    const data = await this.fetchFromServer(userId);
    return this.parseUserData(data);
  }

  private async fetchFromServer(id: number): Promise<string> {
    // 实现细节
    return '';
  }

  private parseUserData(data: string): UserData {
    // 解析逻辑
    return new UserData();
  }
}
```

## 模块系统和导入导出规范

### 1. 导入语句规范

**导入语句位置和顺序**:
```typescript
// ✅ 正确: 所有import语句在文件开头
import { hilog } from '@kit.PerformanceAnalysisKit';
import { BusinessError } from '@kit.BasicServicesKit';
import * as fs from '@ohos.file.fs';
import { CommonConstants } from '@ohos/common';
import { Logger } from '../util/Logger';

// 类和其他声明
class DataProcessor {
  // 实现
}
```

**标准导入语法**:
```typescript
// ✅ 正确: 标准ES6导入语法
import { ComponentA, ComponentB } from '@ohos/componentlibrary';
import * as Utils from '@ohos/common';
import DefaultExport from './DefaultModule';

// ❌ 错误: require语法
import m = require('mod');
```

### 2. 导出语句规范

**标准导出语法**:
```typescript
// ✅ 正确: 命名导出
export class UserService {
  // 实现
}

export interface UserData {
  id: number;
  name: string;
}

export const API_VERSION = '1.0';

// ✅ 正确: 默认导出
export default class MainComponent {
  // 实现
}
```

**Index.ets导出模式**:
```typescript
// ✅ 正确: 统一导出入口
// Index.ets
export { UserService } from './src/main/ets/service/UserService';
export { UserData, UserConfig } from './src/main/ets/model/UserData';
export { UserComponent } from './src/main/ets/component/UserComponent';
export { default as MainView } from './src/main/ets/view/MainView';

// 类型导出
export type { UserCallback } from './src/main/ets/types/UserTypes';
```

## ArkTS编码规范

### 1. 组件定义标准

**组件装饰器使用**:
```typescript
// ✅ 页面级组件
@Entry
@Component
struct MainPage {
  // 实现
}

// ✅ 可复用组件
@Component
export struct SampleCard {
  // 实现
}

// ✅ 性能优化组件
@Component({ freezeWhenInactive: true })
export struct PracticesView {
  // 实现
}
```

**状态管理模式**:
```typescript
// ✅ 全局状态
@StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel =
  AppStorage.get('GlobalInfoModel')!;

// ✅ 父子组件通信
@ObjectLink sampleCard: SampleCardData;
@Prop @Require sampleIndex: number;

// ✅ 跨组件状态
@Consume currentIndex: number;
@Provide('currentIndex') currentIndex: number = 0;
```

### 2. 响应式设计模式

**断点适配**:
```typescript
// ✅ 使用BreakpointType进行响应式设计
new BreakpointType({
  sm: CommonConstants.SPACE_16,
  md: CommonConstants.SPACE_16,
  lg: CommonConstants.SPACE_24,
}).getValue(this.globalInfoModel.currentBreakpoint)
```

### 3. 路由管理规范

**路由定义**:
```typescript
// ✅ 使用NavPathStack进行路由管理
export class PageContext implements IPageContext {
  private readonly pathStack: NavPathStack;

  public openPage(data: RouterParam, animated: boolean = true): void {
    this.pathStack.pushPath({
      name: data.routerName,
      param: data.param,
    }, animated);
  }
}
```

## 错误处理和类型转换规范

### 1. 异常处理最佳实践

**异常类型限制**:
```typescript
// ❌ 错误: 抛出基础类型
throw 404;
throw 'Network error';
throw { code: 500, message: 'Server error' };

// ✅ 正确: 抛出Error类或其派生类
throw new Error('Network connection failed');
throw new TypeError('Expected string but got number');

// ✅ 正确: 自定义错误类
class NetworkError extends Error {
  public code: number;

  constructor(message: string, code: number) {
    super(message);
    this.name = 'NetworkError';
    this.code = code;
  }
}

throw new NetworkError('Connection timeout', 408);
```

**try-catch语句规范**:
```typescript
// ✅ 正确: 标准异常处理
async function fetchUserData(userId: number): Promise<UserData | null> {
  try {
    const response = await httpClient.get(`/users/${userId}`);
    return this.parseUserData(response.data);
  } catch (error) {
    // 不需要类型标注，直接使用
    const err = error as Error;
    Logger.error('Failed to fetch user data', err.message);

    // 根据错误类型进行不同处理
    if (err instanceof NetworkError) {
      showNetworkErrorDialog();
    } else {
      showGenericErrorToast('操作失败，请重试');
    }

    return null;
  }
}
```

### 2. 类型转换和类型保护

**安全类型转换**:
```typescript
// ✅ 正确: 使用as进行类型转换
class Shape {}
class Circle extends Shape {
  radius: number = 0;
}

function processShape(shape: Shape): void {
  // 使用instanceof进行类型检查
  if (shape instanceof Circle) {
    const circle = shape as Circle;
    console.info(`Circle radius: ${circle.radius}`);
  }
}
```

**类型保护函数**:
```typescript
// ✅ 正确: 使用instanceof和as进行类型保护
function isCircle(shape: Object): boolean {
  return shape instanceof Circle;
}

function processShapeSafely(shape: Object): void {
  if (isCircle(shape)) {
    const circle = shape as Circle;
    console.info(`Processing circle with radius: ${circle.radius}`);
  } else {
    Logger.warn('Unknown shape type');
  }
}
```

## 性能优化专项指导

### 1. 组件性能优化

**组件懒加载和冻结**:
```typescript
// ✅ 使用freezeWhenInactive优化性能
@Component({ freezeWhenInactive: true })
export struct HeavyComponent {
  @State data: ComplexData[] = [];

  aboutToAppear(): void {
    // 组件激活时才加载数据
    this.loadData();
  }

  aboutToDisappear(): void {
    // 组件销毁时清理资源
    this.cleanup();
  }
}
```

**状态管理优化**:
```typescript
// ✅ 使用@ObjectLink减少不必要的重渲染
@Observed
class ListItemData {
  id: number = 0;
  title: string = '';
  isSelected: boolean = false;
}

@Component
export struct OptimizedListItem {
  @ObjectLink itemData: ListItemData;

  build() {
    Row() {
      Text(this.itemData.title)
        .fontColor(this.itemData.isSelected ? Color.Blue : Color.Black)
    }
  }
}
```

### 2. 内存管理优化

**资源清理**:
```typescript
// ✅ 正确的资源管理
@Component
export struct ResourceManagedComponent {
  private timer: number | null = null;

  aboutToAppear(): void {
    this.timer = setInterval(() => {
      this.updateData();
    }, 1000);
  }

  aboutToDisappear(): void {
    if (this.timer !== null) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }
}
```

## LLMs代码生成检查清单

### 1. ArkTS语法合规性检查

**基础语法检查**:
- [ ] 使用`let`/`const`而非`var`声明变量
- [ ] 所有变量都有明确的类型标注或可推断类型
- [ ] 禁止使用`any`、`unknown`类型
- [ ] 函数返回类型明确标注（特别是递归调用场景）
- [ ] 使用箭头函数而非函数表达式
- [ ] 类字段在类作用域内显式声明，不在构造函数中声明

**对象和类检查**:
- [ ] 对象属性名为合法标识符，不使用数字或字符串
- [ ] 对象字面量有明确的类型标注
- [ ] 类使用`private`关键字而非`#`声明私有字段
- [ ] 接口继承接口，类实现接口，避免structural typing
- [ ] 不在运行时修改对象布局（添加/删除属性）

**函数和方法检查**:
- [ ] 不在函数内声明函数，使用lambda函数替代
- [ ] 不使用参数解构，直接传递参数
- [ ] `this`仅在类的实例方法中使用
- [ ] 异常抛出仅使用Error类或其派生类

### 2. 类型系统检查

**类型定义检查**:
- [ ] 泛型函数有明确的类型实参或可从参数推断
- [ ] 联合类型使用明确的类型而非条件类型
- [ ] 接口和类型别名命名唯一，不与其他标识符冲突
- [ ] 枚举成员类型一致，不混合不同类型

**类型转换检查**:
- [ ] 使用`as`语法进行类型转换，不使用`<type>`语法
- [ ] 类型保护使用`instanceof`和`as`组合
- [ ] 一元运算符仅用于数值类型
- [ ] 避免隐式类型转换，使用显式转换方法

### 3. 模块系统检查

**导入导出检查**:
- [ ] 所有`import`语句在文件开头
- [ ] 使用标准ES6导入语法，不使用`require`
- [ ] 使用标准`export`语法，不使用`export =`
- [ ] Index.ets文件正确导出模块内容
- [ ] 模块依赖关系符合三层架构原则

### 4. HarmonyOS特定检查

**组件定义检查**:
- [ ] 组件使用正确的装饰器（@Component、@Entry等）
- [ ] 状态管理使用正确的装饰器（@State、@Prop、@ObjectLink等）
- [ ] 组件性能优化（freezeWhenInactive等）
- [ ] 响应式设计使用BreakpointType

**架构合规检查**:
- [ ] 文件组织符合标准目录结构
- [ ] 模块类型配置正确（HAP/HAR）
- [ ] 依赖关系符合分层架构原则
- [ ] 路由配置正确（router_map.json）

### 5. 性能和资源检查

**性能优化检查**:
- [ ] 重型组件使用懒加载和条件渲染
- [ ] 避免在build方法中创建对象
- [ ] 正确使用@ObjectLink减少重渲染
- [ ] 实现适当的资源清理（aboutToDisappear）

**内存管理检查**:
- [ ] 定时器和订阅在组件销毁时清理
- [ ] 大数据集使用分页或虚拟化
- [ ] 缓存策略合理，避免内存泄漏

### 6. 错误处理检查

**异常处理检查**:
- [ ] try-catch语句不标注catch参数类型
- [ ] 异常处理覆盖所有可能的错误场景
- [ ] 错误信息对用户友好
- [ ] 日志记录包含足够的调试信息

**类型安全检查**:
- [ ] null和undefined检查完整
- [ ] 数组和对象访问有边界检查
- [ ] 类型转换有运行时验证
- [ ] 可选属性正确处理

### 7. 代码质量检查

**命名规范检查**:
- [ ] 文件命名使用PascalCase（.ets文件）
- [ ] 类名、接口名使用PascalCase
- [ ] 变量名、方法名使用camelCase
- [ ] 常量使用UPPER_SNAKE_CASE

**代码结构检查**:
- [ ] 单一职责原则，类和方法功能明确
- [ ] 依赖注入而非硬编码依赖
- [ ] 配置外部化，不在代码中硬编码
- [ ] 适当的注释和文档

## 常见问题和解决方案

### 1. 构建问题

**内存不足**:
```bash
# 解决方案: 调整hvigor内存配置
"hvigor.pool.maxSize": 3
"ohos.arkCompile.maxSize": 2
```

**依赖冲突**:
```bash
# 解决方案: 检查oh-package.json5依赖版本
# 确保所有模块使用相同的公共依赖版本
```

### 2. 运行时问题

**路由失败**:
```typescript
// 解决方案: 检查router_map.json配置
// 确保buildFunction名称与实际导出一致
```

**状态同步问题**:
```typescript
// 解决方案: 使用@Watch监听状态变化
@StorageProp('GlobalInfoModel') @Watch('handleStateChange')
globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
```

## 总结

这份规范确保了所有LLMs在HarmonyOS开发中保持一致的代码风格、架构模式和最佳实践。通过遵循这些规范，可以：

1. **确保代码质量**: 生成符合ArkTS规范的高质量代码
2. **提高开发效率**: 减少编译错误和运行时问题
3. **保持架构一致性**: 遵循HarmonyOS项目的分层架构原则
4. **优化性能**: 应用性能最佳实践和内存管理策略
5. **简化维护**: 统一的代码风格和结构便于团队协作

建议LLMs在生成代码时严格按照此规范进行检查和验证，确保输出的代码符合ArkTS要求并遵循HarmonyOS开发最佳实践。
```
```
