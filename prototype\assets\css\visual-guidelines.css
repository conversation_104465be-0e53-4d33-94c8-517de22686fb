/* 
 * 「听汀」视觉设计规范 v2.0
 * 基于empty-state.html成功元素提取的设计规范
 */

/* ========== 顶部导航栏规范 ========== */
.header-container {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px) saturate(180%);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 50;
}

.theme-night .header-container {
  background: rgba(26, 29, 35, 0.85);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.header-brand-icon {
  font-size: 1.5rem;
  color: var(--color-accent);
  filter: drop-shadow(0 2px 4px rgba(212, 175, 55, 0.3));
}

.header-brand-text {
  font-size: 1.25rem;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: 0.05em;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 10px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.header-button:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-theme-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  border-radius: 10px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.header-theme-toggle:hover {
  background: var(--bg-secondary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* ========== 底部控制栏规范 ========== */
.bottom-control-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px) saturate(180%);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 40;
  padding: 1rem;
}

.theme-night .bottom-control-container {
  background: rgba(26, 29, 35, 0.95);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.mini-player-card {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--card-bg);
  border-radius: 16px;
  border: 1px solid var(--border-color);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 0.75rem;
}

.mini-player-cover {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
  flex-shrink: 0;
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.mini-player-info {
  flex: 1;
  min-width: 0;
}

.mini-player-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.875rem;
  line-height: 1.2;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.mini-player-artist {
  color: var(--text-secondary);
  font-size: 0.75rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.control-buttons-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}

.control-button-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
}

.control-button:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.control-button.primary {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  box-shadow: 0 4px 16px rgba(74, 144, 226, 0.3);
}

.control-button.primary:hover {
  background: var(--color-accent);
  border-color: var(--color-accent);
  box-shadow: 0 6px 20px rgba(212, 175, 55, 0.4);
}

.control-button.active {
  color: var(--color-accent);
  background: rgba(212, 175, 55, 0.1);
  border-color: var(--color-accent);
}

/* ========== 播放器页面底部控制区域规范 ========== */
.player-bottom-control {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px) saturate(180%);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 40;
  padding: 1.5rem;
}

.theme-night .player-bottom-control {
  background: rgba(26, 29, 35, 0.95);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.progress-section {
  margin-bottom: 1.5rem;
}

.progress-container {
  position: relative;
  height: 6px;
  background: var(--border-color);
  border-radius: 3px;
  cursor: pointer;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
  border-radius: 3px;
  position: relative;
  transition: width 0.3s ease;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  right: -8px;
  width: 16px;
  height: 16px;
  background: var(--color-accent);
  border-radius: 50%;
  transform: translateY(-50%);
  box-shadow: 0 2px 8px rgba(212, 175, 55, 0.4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.progress-container:hover .progress-thumb {
  opacity: 1;
}

.progress-time {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.player-controls-section {
  margin-bottom: 1rem;
}

.player-control-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
}

.player-control-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.player-control-button:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.player-control-button.play {
  width: 72px;
  height: 72px;
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
  box-shadow: 0 8px 32px rgba(74, 144, 226, 0.4);
}

.player-control-button.play:hover {
  background: var(--color-accent);
  border-color: var(--color-accent);
  box-shadow: 0 12px 40px rgba(212, 175, 55, 0.5);
}

.player-control-button.active {
  color: var(--color-accent);
  background: rgba(212, 175, 55, 0.1);
  border-color: var(--color-accent);
}

.player-secondary-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: var(--border-color);
  border-radius: 2px;
  position: relative;
  cursor: pointer;
}

.volume-fill {
  height: 100%;
  background: var(--color-primary);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* ========== 浮动效果规范 ========== */
.floating-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-primary);
  border-radius: 50%;
  opacity: 0.2;
  animation: float-particle 6s ease-in-out infinite;
}

@keyframes float-particle {
  0%, 100% {
    transform: translateY(0px) translateX(0px);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-20px) translateX(10px);
    opacity: 0.4;
  }
}

/* ========== 毛玻璃效果规范 ========== */
.glass-morphism {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-night .glass-morphism {
  background: rgba(26, 29, 35, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ========== 响应式适配 ========== */
@media (max-width: 768px) {
  .header-container {
    padding: 1rem 1.5rem;
  }
  
  .bottom-control-container {
    padding: 0.75rem 1rem;
  }
  
  .player-bottom-control {
    padding: 1rem;
  }
  
  .control-button {
    width: 40px;
    height: 40px;
  }
  
  .player-control-button {
    width: 48px;
    height: 48px;
  }
  
  .player-control-button.play {
    width: 64px;
    height: 64px;
  }
}
