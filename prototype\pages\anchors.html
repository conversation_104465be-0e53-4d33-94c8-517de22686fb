<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>情感锚地 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .anchors-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .timeline-container {
            position: relative;
            padding-left: 2rem;
        }
        
        .timeline-line {
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--color-primary), var(--color-accent));
        }
        
        .anchor-item {
            position: relative;
            margin-bottom: 2rem;
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }

        .anchor-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }

        .anchor-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .anchor-item::after {
            content: '';
            position: absolute;
            left: -2rem;
            top: 2rem;
            width: 16px;
            height: 16px;
            background: var(--color-accent);
            border-radius: 50%;
            border: 4px solid var(--card-bg);
            box-shadow: 0 0 0 2px var(--color-accent);
        }
        
        .anchor-cover {
            width: 80px;
            height: 80px;
            border-radius: 16px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            flex-shrink: 0;
            margin-right: 1.5rem;
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
            position: relative;
            overflow: hidden;
        }

        .anchor-cover::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        .mood-tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-right: 0.5rem;
            margin-bottom: 0.5rem;
        }
        
        .mood-happy { background: rgba(255, 193, 7, 0.2); color: #ff6b35; }
        .mood-sad { background: rgba(74, 144, 226, 0.2); color: #4a90e2; }
        .mood-calm { background: rgba(40, 167, 69, 0.2); color: #28a745; }
        .mood-excited { background: rgba(220, 53, 69, 0.2); color: #dc3545; }
        
        .anchor-story {
            background: var(--bg-secondary);
            border-radius: 8px;
            padding: 1rem;
            margin-top: 1rem;
            font-style: italic;
            color: var(--text-secondary);
            border-left: 3px solid var(--color-accent);
        }
        
        .stats-overview {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
        }
        
        .filter-tabs {
            display: flex;
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 0.25rem;
            margin-bottom: 2rem;
        }
        
        .filter-tab {
            flex: 1;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: var(--text-secondary);
        }
        
        .filter-tab.active {
            background: var(--color-primary);
            color: white;
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="anchors-container water-bg" x-data="anchorsApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">情感锚地</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="exportAnchors()" class="header-button" aria-label="导出锚点">
                        <iconify-icon icon="material-symbols:download" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 锚点统计 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <div class="stats-overview">
                    <h2 class="title-lg text-primary mb-4">锚点统计</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="anchorStats.total"></div>
                            <div class="text-sm text-secondary">总锚点</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="anchorStats.thisMonth"></div>
                            <div class="text-sm text-secondary">本月新增</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="anchorStats.mostMood"></div>
                            <div class="text-sm text-secondary">最常心情</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="anchorStats.avgPerMonth"></div>
                            <div class="text-sm text-secondary">月均收藏</div>
                        </div>
                    </div>
                </div>
                
                <!-- 筛选标签 -->
                <div class="filter-tabs">
                    <div @click="currentFilter = 'all'" 
                         :class="currentFilter === 'all' ? 'active' : ''" 
                         class="filter-tab">全部</div>
                    <div @click="currentFilter = 'recent'" 
                         :class="currentFilter === 'recent' ? 'active' : ''" 
                         class="filter-tab">最近</div>
                    <div @click="currentFilter = 'mood'" 
                         :class="currentFilter === 'mood' ? 'active' : ''" 
                         class="filter-tab">按心情</div>
                    <div @click="currentFilter = 'genre'" 
                         :class="currentFilter === 'genre' ? 'active' : ''" 
                         class="filter-tab">按风格</div>
                </div>
                
                <!-- 锚点时间轴 -->
                <div class="timeline-container">
                    <div class="timeline-line"></div>
                    
                    <template x-for="anchor in filteredAnchors" :key="anchor.id">
                        <div class="anchor-item">
                            <div class="flex items-start">
                                <div class="anchor-cover">
                                    <iconify-icon icon="material-symbols:anchor" x-show="!anchor.cover"></iconify-icon>
                                    <img :src="anchor.cover" :alt="anchor.title" class="w-full h-full object-cover rounded-xl" x-show="anchor.cover">
                                </div>
                                
                                <div class="flex-1 min-w-0">
                                    <div class="flex items-start justify-between mb-2">
                                        <div>
                                            <h4 class="font-semibold text-primary" x-text="anchor.title"></h4>
                                            <p class="text-sm text-secondary" x-text="anchor.artist + ' - ' + anchor.album"></p>
                                        </div>
                                        <div class="text-xs text-secondary" x-text="formatDate(anchor.anchoredAt)"></div>
                                    </div>
                                    
                                    <!-- 心情标签 -->
                                    <div class="mb-3">
                                        <template x-for="mood in anchor.moods" :key="mood">
                                            <span :class="'mood-' + mood" class="mood-tag" x-text="getMoodText(mood)"></span>
                                        </template>
                                    </div>
                                    
                                    <!-- 锚点故事 -->
                                    <div x-show="anchor.story" class="anchor-story">
                                        <iconify-icon icon="material-symbols:format-quote" class="text-accent mr-2"></iconify-icon>
                                        <span x-text="anchor.story"></span>
                                    </div>
                                    
                                    <!-- 操作按钮 -->
                                    <div class="flex items-center justify-between mt-4">
                                        <div class="flex space-x-3">
                                            <button @click="playSong(anchor)" class="text-sm text-primary hover:text-accent transition-colors">
                                                <iconify-icon icon="material-symbols:play-arrow" class="mr-1"></iconify-icon>
                                                播放
                                            </button>
                                            <button @click="editStory(anchor)" class="text-sm text-secondary hover:text-primary transition-colors">
                                                <iconify-icon icon="material-symbols:edit" class="mr-1"></iconify-icon>
                                                编辑故事
                                            </button>
                                        </div>
                                        
                                        <button @click="removeAnchor(anchor)" class="text-sm text-secondary hover:text-error transition-colors">
                                            <iconify-icon icon="material-symbols:anchor-outline" class="mr-1"></iconify-icon>
                                            取消收藏
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
                
                <!-- 空状态 -->
                <div x-show="filteredAnchors.length === 0" class="text-center py-16">
                    <iconify-icon icon="material-symbols:anchor-outline" class="text-6xl text-secondary opacity-50 mb-4"></iconify-icon>
                    <h3 class="title-md text-secondary mb-2">还没有锚点</h3>
                    <p class="text-secondary">开始收藏你喜欢的音乐，在这里记录情感的停泊</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function anchorsApp() {
            return {
                currentFilter: 'all',
                
                anchorStats: {
                    total: 156,
                    thisMonth: 23,
                    mostMood: '平静',
                    avgPerMonth: 18
                },
                
                anchors: [
                    {
                        id: 1,
                        title: '水调歌头',
                        artist: '古风音乐',
                        album: '诗词歌赋',
                        cover: null,
                        anchoredAt: Date.now() - 86400000,
                        moods: ['calm', 'happy'],
                        story: '在一个月圆之夜，听到这首歌时想起了远方的朋友，心中涌起一阵温暖。'
                    },
                    {
                        id: 2,
                        title: '静夜思',
                        artist: '古典音乐',
                        album: '唐诗三百首',
                        cover: null,
                        anchoredAt: Date.now() - 172800000,
                        moods: ['sad', 'calm'],
                        story: '深夜时分，这首歌陪伴我度过了思乡的夜晚。'
                    },
                    {
                        id: 3,
                        title: '春江花月夜',
                        artist: '古风音乐',
                        album: '诗词歌赋',
                        cover: null,
                        anchoredAt: Date.now() - 259200000,
                        moods: ['excited', 'happy'],
                        story: '春天的第一个温暖午后，阳光透过窗棂，这首歌如画卷般展开。'
                    }
                ],
                
                get filteredAnchors() {
                    let filtered = [...this.anchors];
                    
                    switch (this.currentFilter) {
                        case 'recent':
                            filtered = filtered.sort((a, b) => b.anchoredAt - a.anchoredAt);
                            break;
                        case 'mood':
                            filtered = filtered.sort((a, b) => a.moods[0].localeCompare(b.moods[0]));
                            break;
                        case 'genre':
                            filtered = filtered.sort((a, b) => a.artist.localeCompare(b.artist));
                            break;
                    }
                    
                    return filtered;
                },
                
                getMoodText(mood) {
                    const moodMap = {
                        happy: '开心',
                        sad: '忧伤',
                        calm: '平静',
                        excited: '兴奋'
                    };
                    return moodMap[mood] || mood;
                },
                
                formatDate(timestamp) {
                    const date = new Date(timestamp);
                    const now = new Date();
                    const diff = now - date;
                    const days = Math.floor(diff / 86400000);
                    
                    if (days === 0) return '今天';
                    if (days === 1) return '昨天';
                    if (days < 7) return `${days}天前`;
                    if (days < 30) return `${Math.floor(days / 7)}周前`;
                    return `${Math.floor(days / 30)}个月前`;
                },
                
                playSong(anchor) {
                    console.log('🎵 播放锚点歌曲:', anchor.title);
                    window.location.href = './player.html';
                },
                
                editStory(anchor) {
                    console.log('✏️ 编辑锚点故事:', anchor.title);
                    // 打开编辑故事模态框
                },
                
                removeAnchor(anchor) {
                    console.log('🗑️ 移除锚点:', anchor.title);
                    this.anchors = this.anchors.filter(a => a.id !== anchor.id);
                },
                
                exportAnchors() {
                    console.log('📤 导出锚点数据');
                    // 导出锚点数据为JSON或其他格式
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('⚓ 锚点页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 我的锚点');
        });
    </script>
</body>
</html>
