# 系统架构文档 - 「听汀」HarmonyOS离线音乐播放器

## 1. 架构总览

### 1.1 架构设计目标
- **严格遵循HarmonyOS三层架构**：Products(HAP) → Features(HAR) → Common(HAR)
- **跨设备兼容性**：同时支持手机、平板、2in1设备
- **高性能音频处理**：低延迟、高保真音质
- **离线优先设计**：无网络依赖的纯粹本地体验

### 1.2 技术栈选择
- **开发语言**：ArkTS (TypeScript超集)
- **UI框架**：ArkUI声明式UI
- **状态管理**：@StorageProp + @Link + AppStorage
- **音频引擎**：AVCodecKit + AudioRenderer
- **数据存储**：SQLite + Preferences
- **文件系统**：@ohos.fileio

## 2. 分层架构设计

### 2.1 架构分层图
```
┌─────────────────────────────────────────┐
│              Products层                 │
│         (应用入口HAP包)                 │
│                                         │
│  ┌─────────────┐    ┌─────────────┐    │
│  │  Mobile App │    │  Tablet App │    │
│  │   (entry)   │    │   (entry)   │    │
│  └─────────────┘    └─────────────┘    │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│              Features层                 │
│          (业务模块HAR)                  │
│                                         │
│  ┌─────────────┐    ┌─────────────┐    │
│  │   Audio     │    │   Library   │    │
│  │   Player    │    │   Manager   │    │
│  └─────────────┘    └─────────────┘    │
│                                         │
│  ┌─────────────┐    ┌─────────────┐    │
│  │   Playlist  │    │   Theme     │    │
│  │   System    │    │   Engine    │    │
│  └─────────────┘    └─────────────┘    │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│               Common层                  │
│          (公共能力HAR)                  │
│                                         │
│  ┌─────────────┐    ┌─────────────┐    │
│  │   Audio     │    │   Storage   │    │
│  │   Utils     │    │   Manager   │    │
│  └─────────────┘    └─────────────┘    │
│                                         │
│  ┌─────────────┐    ┌─────────────┐    │
│  │   UI        │    │   Logger    │    │
│  │   Components│    │   System    │    │
│  └─────────────┘    └─────────────┘    │
└─────────────────────────────────────────┘
```

### 2.2 模块详细设计

#### 2.2.1 Products层 (HAP包)
**位置**: `products/`
- **phone**: 手机端应用入口
- **tablet**: 平板端应用入口
- **common**: 共享配置和资源

**phone/entry/src/main/ets/pages/MainPage.ets**
```typescript
@Entry
@Component
struct MainPage {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = 
    AppStorage.get('GlobalInfoModel')!;
  
  build() {
    Navigation(this.globalInfoModel.pathStack) {
      Tabs() {
        TabContent() {
          LibraryView()
        }.tabBar(this.TabBuilder(TabType.LIBRARY))
        
        TabContent() {
          PlayerView()
        }.tabBar(this.TabBuilder(TabType.PLAYER))
        
        TabContent() {
          PlaylistView()
        }.tabBar(this.TabBuilder(TabType.PLAYLIST))
      }
    }
  }
}
```

#### 2.2.2 Features层 (HAR包)

**1. Audio Player模块**
**位置**: `features/audioplayer/`

**核心组件结构**:
```
features/audioplayer/
├── src/main/ets/
│   ├── component/
│   │   ├── AudioPlayerControl.ets   # 播放控制组件
│   │   ├── ProgressBar.ets         # 进度条组件
│   │   └── VolumeControl.ets       # 音量控制组件
│   ├── view/
│   │   ├── PlayerView.ets          # 播放界面
│   │   └── MiniPlayer.ets          # 迷你播放器
│   ├── viewmodel/
│   │   ├── PlayerState.ets         # 播放状态
│   │   └── PlayerViewModel.ets     # 播放器逻辑
│   ├── service/
│   │   ├── AudioService.ets        # 音频服务
│   │   └── PlaybackManager.ets     # 播放管理器
│   └── model/
│       ├── AudioFile.ets           # 音频文件模型
│       └── PlaybackState.ets       # 播放状态模型
```

**2. Library Manager模块**
**位置**: `features/librarymanager/`

**核心功能**:
- 本地音乐文件扫描
- 元数据解析
- 音乐库索引
- 文件夹监控

**3. Playlist System模块**
**位置**: `features/playlistsystem/`

**核心功能**:
- 播放列表CRUD操作
- 智能播放列表生成
- 收藏管理

**4. Theme Engine模块**
**位置**: `features/themeengine/`

**核心功能**:
- 主题切换
- 动态配色
- 响应式适配

#### 2.2.3 Common层 (HAR包)

**1. Audio Utils**
**位置**: `common/audioutils/`

**核心工具类**:
```typescript
// 音频格式识别
export class AudioFormatDetector {
  static detectFormat(filePath: string): AudioFormat {
    // 实现格式检测逻辑
  }
}

// 元数据解析器
export class MetadataParser {
  static parse(filePath: string): Promise<AudioMetadata> {
    // 解析ID3标签等
  }
}
```

**2. Storage Manager**
**位置**: `common/storagemanager/`

**数据模型设计**:
```typescript
@Observed
export class AudioFile {
  id: string = '';
  filePath: string = '';
  title: string = '';
  artist: string = '';
  album: string = '';
  duration: number = 0;
  coverPath?: string;
  lyricsPath?: string;
  playCount: number = 0;
  isFavorite: boolean = false;
  addedDate: Date = new Date();
}

@Observed
export class Playlist {
  id: string = '';
  name: string = '';
  songs: string[] = []; // 音频文件ID数组
  coverPath?: string;
  createdDate: Date = new Date();
}
```

**3. UI Components**
**位置**: `common/uicomponents/`

**共享组件**:
- BaseButton.ets - 基础按钮
- BaseList.ets - 基础列表
- BaseDialog.ets - 基础对话框
- ThemeSwitch.ets - 主题切换组件

**4. Logger System**
**位置**: `app/common/src/main/ets/util/Logger.ets`

**日志管理**:
```typescript
export class Logger {
  private static instance: Logger;
  private domain: number = 0xFF00;
  
  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }
  
  info(tag: string, message: string, ...args: any[]) {
    hilog.info(this.domain, tag, message, ...args);
  }
  
  error(tag: string, message: string, ...args: any[]) {
    hilog.error(this.domain, tag, message, ...args);
  }
}
```

## 3. 技术架构设计

### 3.1 音频处理架构

**音频引擎设计**:
```typescript
// 音频服务接口
export interface IAudioService {
  play(filePath: string): Promise<void>;
  pause(): void;
  resume(): void;
  stop(): void;
  seek(position: number): void;
  setVolume(volume: number): void;
  getDuration(): number;
  getCurrentPosition(): number;
}

// 原生音频实现
export class NativeAudioService implements IAudioService {
  private audioRenderer?: audio.AudioRenderer;
  
  async play(filePath: string): Promise<void> {
    const audioStreamInfo: audio.AudioStreamInfo = {
      samplingRate: audio.AudioSamplingRate.SAMPLE_RATE_44100,
      channels: audio.AudioChannel.CHANNEL_2,
      sampleFormat: audio.AudioSampleFormat.SAMPLE_FORMAT_S16LE,
      encodingType: audio.AudioEncodingType.ENCODING_TYPE_RAW
    };
    
    this.audioRenderer = await audio.createAudioRenderer(audioStreamInfo);
    // 实现播放逻辑
  }
}
```

### 3.2 数据流架构

**数据流图**:
```
文件系统 → 扫描器 → 元数据解析 → SQLite存储 → UI展示
           ↓
        索引构建 → 搜索优化 → 快速检索
```

### 3.3 状态管理架构

**全局状态管理**:
```typescript
// 全局状态定义
@Observed
export class GlobalInfoModel {
  currentBreakpoint: string = 'sm';
  isDarkMode: boolean = false;
  currentTheme: ThemeType = ThemeType.MORNING;
  pathStack: NavPathStack = new NavPathStack();
}

// 注册全局状态
AppStorage.setOrCreate('GlobalInfoModel', new GlobalInfoModel());
```

### 3.4 响应式设计架构

**断点适配**:
```typescript
export class ResponsiveLayout {
  static getGridColumns(breakpoint: string): number {
    return new BreakpointType({
      sm: 1,
      md: 2,
      lg: 3,
      xl: 4
    }).getValue(breakpoint);
  }
}
```

## 4. 性能优化策略

### 4.1 组件优化
- 使用`@Component({ freezeWhenInactive: true })`优化性能
- 实现懒加载机制
- 使用`@Reusable`装饰器复用组件

### 4.2 内存管理
- 音频资源及时释放
- 大图片压缩处理
- 数据库连接池管理

### 4.3 启动优化
- 预加载关键资源
- 延迟初始化非关键组件
- 使用Worker线程处理繁重任务

## 5. 安全与权限设计

### 5.1 权限管理
```json
// module.json5权限配置
{
  "reqPermissions": [
    {
      "name": "ohos.permission.READ_MEDIA",
      "reason": "读取本地音乐文件"
    },
    {
      "name": "ohos.permission.WRITE_MEDIA",
      "reason": "保存播放列表和封面"
    }
  ]
}
```

### 5.2 数据安全
- 敏感数据加密存储
- 文件访问权限最小化
- 用户隐私保护

## 6. 测试架构

### 6.1 测试策略
- **单元测试**：业务逻辑、工具函数
- **集成测试**：模块间交互
- **UI测试**：关键用户流程
- **性能测试**：启动时间、内存使用

### 6.2 测试框架
```typescript
// 测试示例
describe('AudioService', () => {
  it('should play audio file correctly', async () => {
    const service = new NativeAudioService();
    await service.play('/test/test.mp3');
    expect(service.getState()).toBe(PlaybackState.PLAYING);
  });
});
```

## 7. 部署与发布

### 7.1 构建配置
```json5
// build-profile.json5
{
  "buildOptionSet": [
    {
      "name": "release",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": true,
            "files": ["./obfuscation-rules.txt"]
          }
        }
      }
    }
  ]
}
```

### 7.2 发布配置
- 支持多种设备类型配置
- 应用市场元数据准备
- 版本管理策略

## 8. 开发规范

### 8.1 代码规范
- 遵循HarmonyOS命名约定
- 使用TypeScript严格模式
- 统一的错误处理机制

### 8.2 文件组织
```
tingting/
├── products/
│   ├── phone/
│   └── tablet/
├── features/
│   ├── audioplayer/
│   ├── librarymanager/
│   ├── playlistsystem/
│   └── themeengine/
├── common/
│   ├── audioutils/
│   ├── storagemanager/
│   └── uicomponents/
│   └── logger/
└── docs/
    ├── architecture/
    └── stories/
```

## 9. 技术风险与缓解

### 9.1 音频兼容性
- **风险**：不同格式支持问题
- **缓解**：使用FFmpeg作为后备解码器

### 9.2 性能瓶颈
- **风险**：大音乐库扫描缓慢
- **缓解**：增量扫描 + 多线程处理

### 9.3 内存管理
- **风险**：音频播放内存泄漏
- **缓解**：资源生命周期管理 + 监控

## 10. 后续演进路径

### 10.1 短期（MVP后2个月）
- 主题商店功能
- 高级播放控制
- 性能优化

### 10.2 中期（6个月后）
- 分布式音乐播放
- 跨设备同步
- AI推荐算法

### 10.3 长期（1年后）
- 音乐社区功能
- 艺术家合作平台
- 硬件生态整合

---

**架构师总结**：
这个架构设计严格遵循HarmonyOS开发规范，采用三层架构确保模块解耦，使用ArkTS和ArkUI实现高性能的跨设备体验。音频处理采用原生API确保音质，数据管理使用SQLite保证离线功能，响应式设计适配各种屏幕尺寸。

架构已就绪，现在可以开始创建具体的用户故事进行开发。