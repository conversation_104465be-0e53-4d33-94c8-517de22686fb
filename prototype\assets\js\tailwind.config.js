/**
 * 「听汀」Tailwind CSS 配置 v1.0
 * 集成中国风 Neo-Chinese 设计系统变量
 * 支持晨汀/夜汀主题切换
 */

tailwind.config = {
  // 使用 class 模式进行暗色模式适配
  darkMode: 'class',
  
  content: [
    './prototype/pages/*.html',
    './prototype/assets/js/*.js'
  ],
  
  theme: {
    extend: {
      // 色彩系统 - 汀色五调
      colors: {
        // 晨汀主题色彩
        morning: {
          'bg-primary': '#FEFEFE',      // 宣纸白
          'bg-secondary': '#F5F7FA',    // 晨雾灰
          'primary': '#4A90E2',         // 水波青
          'accent': '#D4AF37',          // 锚点金
          'text': '#2C2C2C',            // 墨黑
          'text-secondary': '#6B7280',  // 淡墨
          'border': '#E5E5E5',          // 淡墨分隔
          'card': '#FFFFFF',            // 卡片白
        },
        
        // 夜汀主题色彩
        night: {
          'bg-primary': '#0A0A0A',      // 墨韵黑
          'bg-secondary': '#1A1D23',    // 深夜灰
          'primary': '#2D4A6B',         // 月光蓝
          'accent': '#B8860B',          // 古铜金
          'text': '#E8E8E8',            // 银白
          'text-secondary': '#9CA3AF',  // 月影灰
          'border': '#2A2A2A',          // 重墨分隔
          'card': '#1F2937',            // 卡片深灰
        },
        
        // 状态色彩
        success: '#48BB78',             // 水草绿
        warning: '#ED8936',             // 夕照橙
        error: '#F56565',               // 落日红
        info: '#4299E1',                // 水波蓝
        
        // 语义化色彩别名
        primary: 'var(--color-primary)',
        accent: 'var(--color-accent)',
        'text-primary': 'var(--text-primary)',
        'text-secondary': 'var(--text-secondary)',
        'bg-primary': 'var(--bg-primary)',
        'bg-secondary': 'var(--bg-secondary)',
        'border-color': 'var(--border-color)',
        'card-bg': 'var(--card-bg)',
      },
      
      // 字体系统
      fontFamily: {
        'primary': ['Source Han Sans CN', 'Noto Sans CJK SC', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'sans-serif'],
        'secondary': ['SF Pro Display', 'Roboto', 'system-ui', 'sans-serif'],
        'mono': ['SF Mono', 'Roboto Mono', 'Consolas', 'monospace'],
      },
      
      // 字体大小
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1.5' }],      // 12px
        'sm': ['0.875rem', { lineHeight: '1.5' }],     // 14px
        'base': ['1rem', { lineHeight: '1.6' }],       // 16px
        'lg': ['1.125rem', { lineHeight: '1.6' }],     // 18px
        'xl': ['1.25rem', { lineHeight: '1.5' }],      // 20px
        '2xl': ['1.5rem', { lineHeight: '1.4' }],      // 24px
        '3xl': ['1.875rem', { lineHeight: '1.3' }],    // 30px
        '4xl': ['2.25rem', { lineHeight: '1.2' }],     // 36px
      },
      
      // 字重
      fontWeight: {
        'light': '300',
        'normal': '400',
        'medium': '500',
        'semibold': '600',
        'bold': '700',
        'heavy': '900',
      },
      
      // 间距系统
      spacing: {
        'xs': '0.25rem',    // 4px
        'sm': '0.5rem',     // 8px
        'md': '1rem',       // 16px
        'lg': '1.5rem',     // 24px
        'xl': '2rem',       // 32px
        '2xl': '3rem',      // 48px
        '3xl': '4rem',      // 64px
      },
      
      // 圆角系统
      borderRadius: {
        'sm': '0.25rem',    // 4px
        'md': '0.5rem',     // 8px
        'lg': '1rem',       // 16px
        'xl': '1.5rem',     // 24px
        'full': '9999px',   // 圆形
      },
      
      // 阴影系统 - 水波效果
      boxShadow: {
        'sm': '0 1px 2px var(--shadow-color, rgba(0, 0, 0, 0.08))',
        'md': '0 4px 6px -1px var(--shadow-color, rgba(0, 0, 0, 0.08)), 0 2px 4px -1px var(--shadow-color, rgba(0, 0, 0, 0.08))',
        'lg': '0 10px 15px -3px var(--shadow-color, rgba(0, 0, 0, 0.08)), 0 4px 6px -2px var(--shadow-color, rgba(0, 0, 0, 0.08))',
        'xl': '0 20px 25px -5px var(--shadow-color, rgba(0, 0, 0, 0.08)), 0 10px 10px -5px var(--shadow-color, rgba(0, 0, 0, 0.08))',
        'water': '0 8px 32px rgba(74, 144, 226, 0.12), 0 4px 16px rgba(74, 144, 226, 0.08)',
        'anchor': '0 4px 20px rgba(212, 175, 55, 0.25)',
      },
      
      // 过渡动画
      transitionDuration: {
        'fast': '150ms',
        'normal': '300ms',
        'slow': '500ms',
        'theme': '1200ms',
      },
      
      transitionTimingFunction: {
        'theme': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      
      // 动画
      animation: {
        'ripple': 'ripple 0.6s ease-out',
        'theme-ripple': 'themeRipple 1200ms ease-out',
        'float': 'float 3s ease-in-out infinite',
        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
      },
      
      keyframes: {
        ripple: {
          '0%': { transform: 'scale(0)', opacity: '1' },
          '100%': { transform: 'scale(4)', opacity: '0' },
        },
        themeRipple: {
          '0%': { opacity: '0', transform: 'scale(0)' },
          '50%': { opacity: '0.1', transform: 'scale(1)' },
          '100%': { opacity: '0', transform: 'scale(1.2)' },
        },
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-10px)' },
        },
      },
      
      // 背景图案
      backgroundImage: {
        'water-pattern': "url('data:image/svg+xml,%3Csvg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cg fill=\"none\" fill-rule=\"evenodd\"%3E%3Cg fill=\"%234A90E2\" fill-opacity=\"0.03\"%3E%3Cpath d=\"M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z\"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E')",
        'paper-texture': "url('data:image/svg+xml,%3Csvg width=\"100\" height=\"100\" viewBox=\"0 0 100 100\" xmlns=\"http://www.w3.org/2000/svg\"%3E%3Cdefs%3E%3Cfilter id=\"paper\"%3E%3CfeTurbulence baseFrequency=\"0.04\" numOctaves=\"5\" result=\"noise\" seed=\"1\"/%3E%3CfeColorMatrix in=\"noise\" type=\"saturate\" values=\"0\"/%3E%3C/filter%3E%3C/defs%3E%3Crect width=\"100\" height=\"100\" filter=\"url(%23paper)\" opacity=\"0.4\"/%3E%3C/svg%3E')",
      },
      
      // Z-index 层级
      zIndex: {
        'dropdown': '1000',
        'sticky': '1020',
        'fixed': '1030',
        'modal-backdrop': '1040',
        'modal': '1050',
        'popover': '1060',
        'tooltip': '1070',
        'toast': '1080',
      },
      
      // 断点系统
      screens: {
        'xs': '375px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
    },
  },
  
  plugins: [
    // 自定义插件：水波纹效果
    function({ addUtilities, theme }) {
      const newUtilities = {
        '.ripple-effect': {
          position: 'relative',
          overflow: 'hidden',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: '50%',
            left: '50%',
            width: '0',
            height: '0',
            borderRadius: '50%',
            background: theme('colors.primary'),
            transform: 'translate(-50%, -50%)',
            transition: 'width 0.6s, height 0.6s',
          },
          '&:active::before': {
            width: '300px',
            height: '300px',
            opacity: '0.3',
          },
        },
        
        // 水墨质感
        '.ink-texture': {
          backgroundImage: theme('backgroundImage.paper-texture'),
          backgroundBlendMode: 'multiply',
        },
        
        // 水波背景
        '.water-bg': {
          backgroundImage: theme('backgroundImage.water-pattern'),
        },
        
        // 毛玻璃效果
        '.glass-effect': {
          backdropFilter: 'blur(10px) saturate(180%)',
          backgroundColor: 'rgba(255, 255, 255, 0.72)',
          border: '1px solid rgba(255, 255, 255, 0.125)',
        },
        
        '.glass-effect-dark': {
          backdropFilter: 'blur(10px) saturate(180%)',
          backgroundColor: 'rgba(26, 29, 35, 0.72)',
          border: '1px solid rgba(255, 255, 255, 0.125)',
        },
      };
      
      addUtilities(newUtilities);
    },
  ],
};
