<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据备份恢复 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .backup-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .backup-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .backup-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .backup-option {
            display: flex;
            align-items: center;
            padding: 1.5rem;
            border: 2px solid var(--border-color);
            border-radius: 16px;
            margin-bottom: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-secondary);
        }
        
        .backup-option:hover {
            border-color: var(--color-primary);
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        .backup-option.active {
            border-color: var(--color-primary);
            background: var(--card-bg);
        }
        
        .backup-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .backup-info {
            flex: 1;
        }
        
        .backup-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .backup-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
            line-height: 1.5;
        }
        
        .backup-status {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 0.5rem;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--color-accent);
        }
        
        .status-indicator.success {
            background: #10b981;
        }
        
        .status-indicator.error {
            background: #ef4444;
        }
        
        .status-indicator.warning {
            background: #f59e0b;
        }
        
        .progress-container {
            margin-top: 1rem;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--border-color);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            display: flex;
            justify-content: space-between;
            font-size: 0.75rem;
            color: var(--text-secondary);
        }
        
        .action-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 1.5rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            font-size: 0.875rem;
        }
        
        .action-button.primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
        }
        
        .action-button.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
        }
        
        .action-button.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .action-button.secondary:hover {
            background: var(--card-bg);
            transform: translateY(-2px);
        }
        
        .action-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .backup-history {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .history-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem;
            border-bottom: 1px solid var(--border-color);
        }
        
        .history-item:last-child {
            border-bottom: none;
        }
        
        .history-info {
            flex: 1;
        }
        
        .history-date {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
        }
        
        .history-size {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .history-actions {
            display: flex;
            gap: 0.5rem;
        }
        
        .icon-button {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .icon-button:hover {
            background: var(--card-bg);
            color: var(--text-primary);
            transform: scale(1.1);
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="backup-container water-bg" x-data="backupApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">数据备份恢复</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="refreshBackupList()" class="header-button" aria-label="刷新">
                        <iconify-icon icon="material-symbols:refresh" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 备份恢复内容 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 备份选项 -->
                <div class="backup-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">备份选项</h2>
                    
                    <div class="backup-option" @click="selectBackupType('local')">
                        <div class="backup-icon">
                            <iconify-icon icon="material-symbols:storage"></iconify-icon>
                        </div>
                        <div class="backup-info">
                            <div class="backup-title">本地备份</div>
                            <div class="backup-description">将数据备份到本地存储，快速安全</div>
                            <div class="backup-status">
                                <div class="status-indicator success"></div>
                                <span class="text-xs text-secondary">上次备份：今天 14:30</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="backup-option" @click="selectBackupType('cloud')">
                        <div class="backup-icon">
                            <iconify-icon icon="material-symbols:cloud"></iconify-icon>
                        </div>
                        <div class="backup-info">
                            <div class="backup-title">云端同步</div>
                            <div class="backup-description">同步到云端，多设备访问</div>
                            <div class="backup-status">
                                <div class="status-indicator warning"></div>
                                <span class="text-xs text-secondary">需要登录账户</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="backup-option" @click="selectBackupType('export')">
                        <div class="backup-icon">
                            <iconify-icon icon="material-symbols:download"></iconify-icon>
                        </div>
                        <div class="backup-info">
                            <div class="backup-title">导出数据</div>
                            <div class="backup-description">导出为文件，便于分享和迁移</div>
                            <div class="backup-status">
                                <div class="status-indicator"></div>
                                <span class="text-xs text-secondary">随时可用</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="flex gap-3 mt-6">
                        <button @click="startBackup()" class="action-button primary" :disabled="isBackingUp">
                            <iconify-icon icon="material-symbols:backup" class="text-lg"></iconify-icon>
                            <span x-text="isBackingUp ? '备份中...' : '开始备份'"></span>
                        </button>
                        
                        <button @click="showImportDialog()" class="action-button secondary">
                            <iconify-icon icon="material-symbols:upload" class="text-lg"></iconify-icon>
                            导入数据
                        </button>
                    </div>
                    
                    <!-- 备份进度 -->
                    <div x-show="isBackingUp" class="progress-container">
                        <div class="progress-bar">
                            <div class="progress-fill" :style="`width: ${backupProgress}%`"></div>
                        </div>
                        <div class="progress-text">
                            <span x-text="backupStatus"></span>
                            <span x-text="backupProgress + '%'"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 备份历史 -->
                <div class="backup-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">备份历史</h2>
                    
                    <div class="backup-history">
                        <template x-for="backup in backupHistory" :key="backup.id">
                            <div class="history-item">
                                <div class="history-info">
                                    <div class="history-date" x-text="backup.date"></div>
                                    <div class="history-size" x-text="backup.size + ' • ' + backup.type"></div>
                                </div>
                                <div class="history-actions">
                                    <button @click="restoreBackup(backup)" class="icon-button" title="恢复">
                                        <iconify-icon icon="material-symbols:restore" class="text-sm"></iconify-icon>
                                    </button>
                                    <button @click="downloadBackup(backup)" class="icon-button" title="下载">
                                        <iconify-icon icon="material-symbols:download" class="text-sm"></iconify-icon>
                                    </button>
                                    <button @click="deleteBackup(backup)" class="icon-button" title="删除">
                                        <iconify-icon icon="material-symbols:delete" class="text-sm"></iconify-icon>
                                    </button>
                                </div>
                            </div>
                        </template>
                    </div>
                    
                    <div x-show="backupHistory.length === 0" class="text-center py-8">
                        <iconify-icon icon="material-symbols:backup" class="text-4xl text-secondary opacity-50 mb-4"></iconify-icon>
                        <h3 class="text-lg font-semibold text-secondary mb-2">暂无备份记录</h3>
                        <p class="text-secondary">创建第一个备份来保护你的数据</p>
                    </div>
                </div>
                
                <!-- 数据统计 -->
                <div class="backup-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">数据统计</h2>
                    
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="text-center p-4 bg-bg-secondary rounded-lg">
                            <div class="text-2xl font-bold text-primary mb-1">1,247</div>
                            <div class="text-sm text-secondary">歌曲数量</div>
                        </div>
                        <div class="text-center p-4 bg-bg-secondary rounded-lg">
                            <div class="text-2xl font-bold text-primary mb-1">89</div>
                            <div class="text-sm text-secondary">播放列表</div>
                        </div>
                        <div class="text-center p-4 bg-bg-secondary rounded-lg">
                            <div class="text-2xl font-bold text-primary mb-1">156</div>
                            <div class="text-sm text-secondary">收藏歌曲</div>
                        </div>
                        <div class="text-center p-4 bg-bg-secondary rounded-lg">
                            <div class="text-2xl font-bold text-primary mb-1">8.5GB</div>
                            <div class="text-sm text-secondary">数据大小</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function backupApp() {
            return {
                isBackingUp: false,
                backupProgress: 0,
                backupStatus: '准备备份...',
                selectedBackupType: 'local',
                
                backupHistory: [
                    {
                        id: 1,
                        date: '2024年7月19日 14:30',
                        size: '8.5GB',
                        type: '完整备份'
                    },
                    {
                        id: 2,
                        date: '2024年7月18日 09:15',
                        size: '2.1GB',
                        type: '增量备份'
                    },
                    {
                        id: 3,
                        date: '2024年7月17日 16:45',
                        size: '8.3GB',
                        type: '完整备份'
                    }
                ],
                
                selectBackupType(type) {
                    this.selectedBackupType = type;
                    console.log('选择备份类型:', type);
                },
                
                async startBackup() {
                    this.isBackingUp = true;
                    this.backupProgress = 0;
                    
                    const steps = [
                        '扫描音乐文件...',
                        '备份播放列表...',
                        '备份用户设置...',
                        '压缩数据...',
                        '完成备份'
                    ];
                    
                    for (let i = 0; i < steps.length; i++) {
                        this.backupStatus = steps[i];
                        await new Promise(resolve => setTimeout(resolve, 1000));
                        this.backupProgress = ((i + 1) / steps.length) * 100;
                    }
                    
                    // 添加新的备份记录
                    const newBackup = {
                        id: Date.now(),
                        date: new Date().toLocaleString('zh-CN'),
                        size: '8.6GB',
                        type: '完整备份'
                    };
                    this.backupHistory.unshift(newBackup);
                    
                    this.isBackingUp = false;
                    console.log('✅ 备份完成');
                },
                
                restoreBackup(backup) {
                    console.log('🔄 恢复备份:', backup.date);
                    if (confirm('确定要恢复此备份吗？当前数据将被覆盖。')) {
                        // 实现恢复逻辑
                    }
                },
                
                downloadBackup(backup) {
                    console.log('📥 下载备份:', backup.date);
                },
                
                deleteBackup(backup) {
                    console.log('🗑️ 删除备份:', backup.date);
                    if (confirm('确定要删除此备份吗？')) {
                        const index = this.backupHistory.findIndex(b => b.id === backup.id);
                        if (index > -1) {
                            this.backupHistory.splice(index, 1);
                        }
                    }
                },
                
                showImportDialog() {
                    console.log('📤 导入数据');
                    // 实现导入对话框
                },
                
                refreshBackupList() {
                    console.log('🔄 刷新备份列表');
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('💾 数据备份恢复页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 数据备份恢复');
        });
    </script>
</body>
</html>
