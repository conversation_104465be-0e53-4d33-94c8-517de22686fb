<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>联系我们 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .contact-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .contact-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .contact-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .contact-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }
        
        .contact-method {
            padding: 2rem;
            border: 2px solid var(--border-color);
            border-radius: 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-secondary);
            position: relative;
            overflow: hidden;
        }
        
        .contact-method:hover {
            border-color: var(--color-primary);
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        }
        
        .contact-method::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(74, 144, 226, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        .contact-method:hover::before {
            left: 100%;
        }
        
        .contact-icon {
            width: 64px;
            height: 64px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem;
            position: relative;
            z-index: 1;
        }
        
        .contact-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .contact-info {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
        
        .contact-time {
            color: var(--color-accent);
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .form-container {
            background: var(--bg-secondary);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid var(--border-color);
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            width: 100%;
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            background: var(--card-bg);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.1);
            transform: translateY(-2px);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .submit-button {
            width: 100%;
            padding: 1rem;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
            border: none;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 1rem;
        }
        
        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
        }
        
        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .faq-section {
            margin-top: 2rem;
        }
        
        .faq-item {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            margin-bottom: 1rem;
            overflow: hidden;
            background: var(--bg-secondary);
        }
        
        .faq-question {
            padding: 1.5rem;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }
        
        .faq-question:hover {
            background: var(--card-bg);
        }
        
        .faq-question.active {
            background: var(--card-bg);
            border-bottom: 1px solid var(--border-color);
        }
        
        .faq-answer {
            padding: 1.5rem;
            background: var(--card-bg);
            color: var(--text-secondary);
            line-height: 1.6;
            display: none;
        }
        
        .faq-answer.show {
            display: block;
        }
        
        .team-info {
            background: linear-gradient(135deg, var(--color-primary)/10, var(--color-accent)/10);
            border-radius: 16px;
            padding: 2rem;
            text-align: center;
            border: 1px solid var(--color-primary)/20;
        }
        
        .team-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
            margin: 0 auto 1rem;
        }
        
        .response-promise {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 1.5rem;
            border-left: 4px solid var(--color-accent);
            margin-top: 2rem;
        }
        
        .promise-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .promise-text {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="contact-container water-bg" x-data="contactApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">联系我们</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="openHelp()" class="header-button" aria-label="帮助">
                        <iconify-icon icon="material-symbols:help" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 联系内容 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 联系方式 -->
                <div class="contact-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">联系方式</h2>
                    
                    <div class="contact-methods">
                        <div @click="contactEmail()" class="contact-method">
                            <div class="contact-icon">
                                <iconify-icon icon="material-symbols:email"></iconify-icon>
                            </div>
                            <div class="contact-title">邮件支持</div>
                            <div class="contact-info"><EMAIL></div>
                            <div class="contact-time">24小时内回复</div>
                        </div>
                        
                        <div @click="contactWeChat()" class="contact-method">
                            <div class="contact-icon">
                                <iconify-icon icon="material-symbols:wechat"></iconify-icon>
                            </div>
                            <div class="contact-title">微信客服</div>
                            <div class="contact-info">TingTing_Support</div>
                            <div class="contact-time">工作日 9:00-18:00</div>
                        </div>
                        
                        <div @click="contactPhone()" class="contact-method">
                            <div class="contact-icon">
                                <iconify-icon icon="material-symbols:phone"></iconify-icon>
                            </div>
                            <div class="contact-title">电话支持</div>
                            <div class="contact-info">************</div>
                            <div class="contact-time">工作日 9:00-21:00</div>
                        </div>
                        
                        <div @click="contactForum()" class="contact-method">
                            <div class="contact-icon">
                                <iconify-icon icon="material-symbols:forum"></iconify-icon>
                            </div>
                            <div class="contact-title">用户论坛</div>
                            <div class="contact-info">community.tingting.app</div>
                            <div class="contact-time">社区互助</div>
                        </div>
                    </div>
                </div>
                
                <!-- 反馈表单 -->
                <div class="contact-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">问题反馈</h2>
                    
                    <div class="form-container">
                        <form @submit.prevent="submitFeedback()">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div class="form-group">
                                    <label class="form-label">姓名</label>
                                    <input type="text" x-model="feedback.name" class="form-input" placeholder="请输入您的姓名" required>
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">邮箱</label>
                                    <input type="email" x-model="feedback.email" class="form-input" placeholder="请输入邮箱地址" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">问题类型</label>
                                <select x-model="feedback.type" class="form-select" required>
                                    <option value="">请选择问题类型</option>
                                    <option value="bug">Bug报告</option>
                                    <option value="feature">功能建议</option>
                                    <option value="usage">使用问题</option>
                                    <option value="business">商务合作</option>
                                    <option value="other">其他</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">问题标题</label>
                                <input type="text" x-model="feedback.subject" class="form-input" placeholder="简要描述您的问题" required>
                            </div>
                            
                            <div class="form-group">
                                <label class="form-label">详细描述</label>
                                <textarea x-model="feedback.message" class="form-textarea" placeholder="请详细描述您的问题或建议，我们会认真对待每一条反馈..." required></textarea>
                            </div>
                            
                            <button type="submit" class="submit-button" :disabled="isSubmitting">
                                <span x-text="isSubmitting ? '提交中...' : '提交反馈'"></span>
                            </button>
                        </form>
                        
                        <!-- 响应承诺 -->
                        <div class="response-promise">
                            <div class="promise-title">我们的承诺</div>
                            <div class="promise-text">
                                我们承诺在24小时内回复您的邮件，在工作时间内1小时内回复微信和电话咨询。
                                您的每一条反馈都是我们改进产品的宝贵意见。
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 常见问题 -->
                <div class="contact-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">常见问题</h2>
                    
                    <div class="faq-section">
                        <template x-for="faq in faqs" :key="faq.id">
                            <div class="faq-item">
                                <div @click="toggleFAQ(faq.id)" 
                                     class="faq-question"
                                     :class="openFAQs.includes(faq.id) ? 'active' : ''">
                                    <span class="font-medium text-primary" x-text="faq.question"></span>
                                    <iconify-icon :icon="openFAQs.includes(faq.id) ? 'material-symbols:expand-less' : 'material-symbols:expand-more'" 
                                                  class="text-lg text-secondary"></iconify-icon>
                                </div>
                                <div class="faq-answer" :class="openFAQs.includes(faq.id) ? 'show' : ''" x-text="faq.answer"></div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- 团队信息 -->
                <div class="contact-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">关于团队</h2>
                    
                    <div class="team-info">
                        <div class="team-avatar">
                            <iconify-icon icon="material-symbols:group"></iconify-icon>
                        </div>
                        <h3 class="text-lg font-semibold text-primary mb-2">听汀开发团队</h3>
                        <p class="text-secondary mb-4">
                            我们是一支专注于音乐体验的小团队，致力于为用户打造最纯粹、最美好的音乐播放体验。
                            我们相信音乐的力量，也相信技术能让音乐更加动人。
                        </p>
                        <div class="flex justify-center space-x-4 text-sm text-secondary">
                            <div>📍 北京·中国</div>
                            <div>👥 5人团队</div>
                            <div>🎵 专注音乐</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function contactApp() {
            return {
                isSubmitting: false,
                openFAQs: [],
                
                feedback: {
                    name: '',
                    email: '',
                    type: '',
                    subject: '',
                    message: ''
                },
                
                faqs: [
                    {
                        id: 1,
                        question: '如何联系技术支持？',
                        answer: '您可以通过邮件、微信、电话或用户论坛联系我们。邮件支持24小时内回复，微信和电话在工作时间内1小时内回复。'
                    },
                    {
                        id: 2,
                        question: '反馈的问题多久能得到回复？',
                        answer: '我们承诺在24小时内回复邮件反馈，紧急问题会优先处理。复杂的技术问题可能需要更多时间调查。'
                    },
                    {
                        id: 3,
                        question: '如何提交功能建议？',
                        answer: '您可以通过反馈表单选择"功能建议"类型，详细描述您希望的功能。我们会认真评估每个建议。'
                    },
                    {
                        id: 4,
                        question: '商务合作如何联系？',
                        answer: '商务合作请发送邮件至 <EMAIL> 或在反馈表单中选择"商务合作"类型。'
                    }
                ],
                
                toggleFAQ(faqId) {
                    const index = this.openFAQs.indexOf(faqId);
                    if (index > -1) {
                        this.openFAQs.splice(index, 1);
                    } else {
                        this.openFAQs.push(faqId);
                    }
                },
                
                async submitFeedback() {
                    this.isSubmitting = true;
                    
                    try {
                        // 模拟提交
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        console.log('📝 提交反馈:', this.feedback);
                        
                        // 重置表单
                        this.feedback = {
                            name: '',
                            email: '',
                            type: '',
                            subject: '',
                            message: ''
                        };
                        
                        alert('反馈提交成功！我们会尽快回复您。');
                        
                    } catch (error) {
                        console.error('提交失败:', error);
                        alert('提交失败，请稍后重试。');
                    } finally {
                        this.isSubmitting = false;
                    }
                },
                
                contactEmail() {
                    window.open('mailto:<EMAIL>');
                    console.log('📧 打开邮件客户端');
                },
                
                contactWeChat() {
                    console.log('💬 联系微信客服');
                    alert('请添加微信号：TingTing_Support');
                },
                
                contactPhone() {
                    console.log('📞 拨打客服电话');
                    alert('客服电话：************\n工作时间：9:00-21:00');
                },
                
                contactForum() {
                    console.log('🌐 打开用户论坛');
                    window.open('https://community.tingting.app', '_blank');
                },
                
                openHelp() {
                    window.location.href = './help.html';
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('📞 联系我们页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 联系我们');
        });
    </script>
</body>
</html>
