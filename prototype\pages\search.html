<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>寻音觅汀 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        /* 搜索页面特殊样式 */
        .search-container {
            background: linear-gradient(135deg, 
                var(--bg-primary) 0%, 
                var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .search-box {
            position: relative;
            background: var(--card-bg);
            border: 2px solid var(--border-color);
            border-radius: 24px;
            padding: 1.25rem 1.75rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .search-box:focus-within {
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.15), 0 8px 32px rgba(0, 0, 0, 0.12);
            transform: translateY(-3px);
        }
        
        .search-box::before {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(45deg, var(--color-primary), var(--color-accent));
            border-radius: 20px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: -1;
        }
        
        .search-box:focus-within::before {
            opacity: 0.1;
        }
        
        .search-input {
            width: 100%;
            background: transparent;
            border: none;
            outline: none;
            font-size: 1.25rem;
            color: var(--text-primary);
            padding-left: 3.5rem;
            font-weight: 500;
        }
        
        .search-input::placeholder {
            color: var(--text-secondary);
        }
        
        .search-icon {
            position: absolute;
            left: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
            transition: color 0.3s ease;
        }
        
        .search-box:focus-within .search-icon {
            color: var(--color-primary);
        }
        
        .history-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        
        .history-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .hot-search-tag {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            color: var(--text-secondary);
            font-size: 0.875rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .hot-search-tag:hover {
            background: var(--color-primary);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
        }
        
        .search-result-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
        }
        
        .search-result-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .search-result-item:last-child {
            border-bottom: none;
        }
        
        .result-cover {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            flex-shrink: 0;
            margin-right: 1rem;
        }
        
        .result-type-badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background: var(--color-accent);
            color: white;
            font-size: 0.75rem;
            border-radius: 4px;
            margin-left: 0.5rem;
        }
        
        .floating-suggestions {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--card-bg);
            border: 1px solid var(--border-color);
            border-radius: 12px;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(10px);
            z-index: 1000;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .suggestion-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-bottom: 1px solid var(--border-color);
        }
        
        .suggestion-item:hover {
            background: var(--bg-secondary);
        }
        
        .suggestion-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body class="font-primary">
    <!-- 主题管理器 -->
    <script src="../assets/js/theme.js"></script>
    
    <!-- 搜索页面 -->
    <div class="search-container water-bg" x-data="searchApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">寻音觅汀</h1>
                </div>
                
                <div class="header-actions">
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 搜索框区域 -->
        <div class="px-6 py-8">
            <div class="max-w-2xl mx-auto">
                <div class="search-box relative" x-data="{ showSuggestions: false }">
                    <iconify-icon icon="material-symbols:search" class="search-icon text-xl"></iconify-icon>
                    <input 
                        type="text" 
                        class="search-input"
                        placeholder="搜索歌曲、专辑、艺术家..."
                        x-model="searchQuery"
                        @input="handleSearchInput()"
                        @focus="showSuggestions = true"
                        @blur="setTimeout(() => showSuggestions = false, 200)"
                    >
                    
                    <!-- 搜索建议 -->
                    <div x-show="showSuggestions && suggestions.length > 0" 
                         x-transition 
                         class="floating-suggestions">
                        <template x-for="suggestion in suggestions" :key="suggestion.id">
                            <div @click="selectSuggestion(suggestion)" class="suggestion-item">
                                <div class="flex items-center">
                                    <iconify-icon icon="material-symbols:search" class="text-secondary mr-3"></iconify-icon>
                                    <span x-text="suggestion.text" class="text-primary"></span>
                                    <span x-text="suggestion.type" class="result-type-badge ml-auto"></span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 搜索内容区域 -->
        <div class="px-6 pb-32">
            <div class="max-w-4xl mx-auto">
                <!-- 搜索历史 -->
                <div x-show="!searchQuery && searchHistory.length > 0" class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="title-md text-primary">搜索历史</h2>
                        <button @click="clearHistory()" class="text-sm text-secondary hover:text-primary transition-colors">
                            清除历史
                        </button>
                    </div>
                    <div class="space-y-2">
                        <template x-for="item in searchHistory" :key="item.id">
                            <div @click="searchQuery = item.query; performSearch()" class="history-item">
                                <iconify-icon icon="material-symbols:history" class="text-secondary mr-3"></iconify-icon>
                                <span x-text="item.query" class="text-primary flex-1"></span>
                                <button @click.stop="removeFromHistory(item.id)" class="text-secondary hover:text-error transition-colors">
                                    <iconify-icon icon="material-symbols:close" class="text-sm"></iconify-icon>
                                </button>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- 热门搜索 -->
                <div x-show="!searchQuery" class="mb-8">
                    <h2 class="title-md text-primary mb-4">热门搜索</h2>
                    <div class="flex flex-wrap gap-3">
                        <template x-for="tag in hotSearches" :key="tag">
                            <span @click="searchQuery = tag; performSearch()" 
                                  x-text="tag" 
                                  class="hot-search-tag"></span>
                        </template>
                    </div>
                </div>
                
                <!-- 搜索结果 -->
                <div x-show="searchQuery && searchResults.length > 0">
                    <h2 class="title-md text-primary mb-4">
                        搜索结果 
                        <span class="text-sm text-secondary">(<span x-text="searchResults.length"></span> 个结果)</span>
                    </h2>
                    
                    <!-- 结果分类标签 -->
                    <div class="flex space-x-4 mb-6">
                        <button @click="resultFilter = 'all'" 
                                :class="resultFilter === 'all' ? 'text-primary border-primary' : 'text-secondary border-transparent'"
                                class="pb-2 border-b-2 transition-colors">
                            全部
                        </button>
                        <button @click="resultFilter = 'songs'" 
                                :class="resultFilter === 'songs' ? 'text-primary border-primary' : 'text-secondary border-transparent'"
                                class="pb-2 border-b-2 transition-colors">
                            歌曲
                        </button>
                        <button @click="resultFilter = 'albums'" 
                                :class="resultFilter === 'albums' ? 'text-primary border-primary' : 'text-secondary border-transparent'"
                                class="pb-2 border-b-2 transition-colors">
                            专辑
                        </button>
                        <button @click="resultFilter = 'artists'" 
                                :class="resultFilter === 'artists' ? 'text-primary border-primary' : 'text-secondary border-transparent'"
                                class="pb-2 border-b-2 transition-colors">
                            艺术家
                        </button>
                    </div>
                    
                    <!-- 搜索结果列表 -->
                    <div class="space-y-2">
                        <template x-for="result in filteredResults" :key="result.id">
                            <div @click="selectResult(result)" class="search-result-item">
                                <div class="result-cover">
                                    <iconify-icon :icon="getResultIcon(result.type)" x-show="!result.cover"></iconify-icon>
                                    <img :src="result.cover" :alt="result.title" class="w-full h-full object-cover rounded-lg" x-show="result.cover">
                                </div>
                                
                                <div class="flex-1 min-w-0">
                                    <h4 class="font-medium text-primary truncate" x-text="result.title"></h4>
                                    <p class="text-sm text-secondary truncate" x-text="result.subtitle"></p>
                                </div>
                                
                                <span x-text="result.type" class="result-type-badge"></span>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- 无搜索结果 -->
                <div x-show="searchQuery && searchResults.length === 0 && !isSearching" class="text-center py-16">
                    <iconify-icon icon="material-symbols:search-off" class="text-6xl text-secondary opacity-50 mb-4"></iconify-icon>
                    <h3 class="title-md text-secondary mb-2">未找到相关内容</h3>
                    <p class="text-secondary">试试其他关键词或检查拼写</p>
                </div>
                
                <!-- 搜索加载状态 -->
                <div x-show="isSearching" class="text-center py-16">
                    <div class="loading-spinner w-8 h-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
                    <p class="text-secondary">正在搜索...</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function searchApp() {
            return {
                searchQuery: '',
                searchHistory: [
                    { id: 1, query: '水调歌头', timestamp: Date.now() - 3600000 },
                    { id: 2, query: '古风音乐', timestamp: Date.now() - 7200000 },
                    { id: 3, query: '静夜思', timestamp: Date.now() - 86400000 }
                ],
                hotSearches: ['古风', '民谣', '流行', '摇滚', '古典', '爵士', '电子', '说唱'],
                suggestions: [],
                searchResults: [],
                resultFilter: 'all',
                isSearching: false,
                
                get filteredResults() {
                    if (this.resultFilter === 'all') {
                        return this.searchResults;
                    }
                    return this.searchResults.filter(result => result.type === this.resultFilter.slice(0, -1));
                },
                
                handleSearchInput() {
                    if (this.searchQuery.length > 0) {
                        this.generateSuggestions();
                        this.performSearch();
                    } else {
                        this.suggestions = [];
                        this.searchResults = [];
                    }
                },
                
                generateSuggestions() {
                    // 模拟搜索建议
                    const mockSuggestions = [
                        { id: 1, text: this.searchQuery + ' 歌曲', type: '歌曲' },
                        { id: 2, text: this.searchQuery + ' 专辑', type: '专辑' },
                        { id: 3, text: this.searchQuery + ' 艺术家', type: '艺术家' }
                    ];
                    this.suggestions = mockSuggestions.slice(0, 3);
                },
                
                async performSearch() {
                    if (!this.searchQuery.trim()) return;
                    
                    this.isSearching = true;
                    
                    // 模拟搜索延迟
                    await new Promise(resolve => setTimeout(resolve, 800));
                    
                    // 模拟搜索结果
                    this.searchResults = [
                        {
                            id: 1,
                            type: 'song',
                            title: '水调歌头',
                            subtitle: '古风音乐 - 诗词歌赋',
                            cover: null
                        },
                        {
                            id: 2,
                            type: 'album',
                            title: '诗词歌赋',
                            subtitle: '古风音乐 - 2024',
                            cover: null
                        },
                        {
                            id: 3,
                            type: 'artist',
                            title: '古风音乐',
                            subtitle: '中国风音乐人',
                            cover: null
                        }
                    ];
                    
                    this.isSearching = false;
                    this.addToHistory(this.searchQuery);
                },
                
                selectSuggestion(suggestion) {
                    this.searchQuery = suggestion.text;
                    this.performSearch();
                },
                
                selectResult(result) {
                    console.log('选择搜索结果:', result);
                    // 根据结果类型跳转到相应页面
                    switch (result.type) {
                        case 'song':
                            window.location.href = './song-detail.html';
                            break;
                        case 'album':
                            window.location.href = './album.html';
                            break;
                        case 'artist':
                            window.location.href = './artist.html';
                            break;
                    }
                },
                
                addToHistory(query) {
                    const existing = this.searchHistory.find(item => item.query === query);
                    if (!existing) {
                        this.searchHistory.unshift({
                            id: Date.now(),
                            query: query,
                            timestamp: Date.now()
                        });
                        // 限制历史记录数量
                        if (this.searchHistory.length > 10) {
                            this.searchHistory = this.searchHistory.slice(0, 10);
                        }
                    }
                },
                
                removeFromHistory(id) {
                    this.searchHistory = this.searchHistory.filter(item => item.id !== id);
                },
                
                clearHistory() {
                    this.searchHistory = [];
                },
                
                getResultIcon(type) {
                    switch (type) {
                        case 'song':
                            return 'material-symbols:music-note';
                        case 'album':
                            return 'material-symbols:album';
                        case 'artist':
                            return 'material-symbols:person';
                        default:
                            return 'material-symbols:music-note';
                    }
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('🔍 搜索页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 搜索页面');
        });
    </script>
</body>
</html>
