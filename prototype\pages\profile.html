<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人汀岸 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        /* 个人页面特殊样式 */
        .profile-container {
            background: linear-gradient(135deg, 
                var(--bg-primary) 0%, 
                var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 12px 40px rgba(74, 144, 226, 0.3);
        }
        
        .profile-avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .stats-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            text-align: center;
            min-height: 140px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .stats-card:hover {
            transform: translateY(-6px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--color-primary);
            line-height: 1;
            margin-bottom: 0.5rem;
        }

        .stats-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            font-weight: 500;
        }

        .stats-icon {
            color: var(--color-accent);
            font-size: 2rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .stats-card:hover .stats-icon {
            transform: scale(1.1);
            color: var(--color-primary);
        }
        
        .recent-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
        }
        
        .recent-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .recent-item:last-child {
            border-bottom: none;
        }
        
        .recent-cover {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.25rem;
            flex-shrink: 0;
            margin-right: 1rem;
        }
        
        .quick-action {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 1.5rem;
            background: var(--card-bg);
            border-radius: 16px;
            border: 1px solid var(--border-color);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            text-decoration: none;
            backdrop-filter: blur(10px);
        }
        
        .quick-action:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            background: var(--bg-secondary);
        }
        
        .quick-action-icon {
            font-size: 2rem;
            color: var(--color-primary);
            margin-bottom: 0.75rem;
        }
        
        .quick-action-text {
            color: var(--text-primary);
            font-weight: 500;
            text-align: center;
        }
        
        .chart-container {
            position: relative;
            height: 200px;
            background: var(--card-bg);
            border-radius: 16px;
            padding: 1rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
        }
        
        .preference-tag {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin: 0.25rem;
        }
        
        .preference-tag.active {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }
    </style>
</head>
<body class="font-primary">
    <!-- 主题管理器 -->
    <script src="../assets/js/theme.js"></script>
    
    <!-- 个人页面 -->
    <div class="profile-container water-bg" x-data="profileApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">个人汀岸</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="editProfile()" class="header-button" aria-label="编辑资料">
                        <iconify-icon icon="material-symbols:edit" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                    
                    <button @click="openSettings()" class="header-button" aria-label="设置">
                        <iconify-icon icon="material-symbols:settings" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 个人信息区域 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 头像和基本信息 -->
                <div class="text-center mb-8">
                    <div class="profile-avatar mx-auto mb-4">
                        <iconify-icon icon="material-symbols:person" x-show="!userInfo.avatar"></iconify-icon>
                        <img :src="userInfo.avatar" :alt="userInfo.name" class="w-full h-full object-cover rounded-full" x-show="userInfo.avatar">
                    </div>
                    
                    <h2 class="title-lg text-primary mb-2" x-text="userInfo.name"></h2>
                    <p class="text-secondary mb-4" x-text="userInfo.bio"></p>
                    
                    <div class="flex items-center justify-center space-x-6 text-sm text-secondary">
                        <div>
                            <span class="font-medium">加入时间</span>
                            <span x-text="userInfo.joinDate"></span>
                        </div>
                        <div>
                            <span class="font-medium">听歌时长</span>
                            <span x-text="formatDuration(userInfo.totalListeningTime)"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 统计卡片 -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                    <div class="stats-card">
                        <iconify-icon icon="material-symbols:anchor" class="stats-icon"></iconify-icon>
                        <div class="stats-number" x-text="userStats.favorites"></div>
                        <div class="stats-label">收藏歌曲</div>
                    </div>
                    
                    <div class="stats-card">
                        <iconify-icon icon="material-symbols:album" class="stats-icon"></iconify-icon>
                        <div class="stats-number" x-text="userStats.albums"></div>
                        <div class="stats-label">收藏专辑</div>
                    </div>
                    
                    <div class="stats-card">
                        <iconify-icon icon="material-symbols:playlist-play" class="stats-icon"></iconify-icon>
                        <div class="stats-number" x-text="userStats.playlists"></div>
                        <div class="stats-label">播放列表</div>
                    </div>
                    
                    <div class="stats-card">
                        <iconify-icon icon="material-symbols:person" class="stats-icon"></iconify-icon>
                        <div class="stats-number" x-text="userStats.artists"></div>
                        <div class="stats-label">关注艺术家</div>
                    </div>
                </div>
                
                <!-- 快捷操作 -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                    <a href="./anchors.html" class="quick-action">
                        <iconify-icon icon="material-symbols:anchor" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">我的锚点</span>
                    </a>
                    
                    <a href="./playlist.html" class="quick-action">
                        <iconify-icon icon="material-symbols:playlist-play" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">播放列表</span>
                    </a>
                    
                    <a href="./music-space.html" class="quick-action">
                        <iconify-icon icon="material-symbols:auto-awesome" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">音乐空间</span>
                    </a>
                    
                    <a href="./library.html" class="quick-action">
                        <iconify-icon icon="material-symbols:library-music" class="quick-action-icon"></iconify-icon>
                        <span class="quick-action-text">音乐库</span>
                    </a>
                </div>
                
                <!-- 最近播放 -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="title-md text-primary">最近播放</h3>
                        <button @click="viewAllHistory()" class="text-sm text-secondary hover:text-primary transition-colors">
                            查看全部
                        </button>
                    </div>
                    
                    <div class="bg-card-bg rounded-xl border border-border-color backdrop-filter backdrop-blur-lg">
                        <template x-for="item in recentPlayed.slice(0, 5)" :key="item.id">
                            <div @click="playSong(item)" class="recent-item">
                                <div class="recent-cover">
                                    <iconify-icon icon="material-symbols:music-note" x-show="!item.cover"></iconify-icon>
                                    <img :src="item.cover" :alt="item.title" class="w-full h-full object-cover rounded-lg" x-show="item.cover">
                                </div>
                                
                                <div class="flex-1 min-w-0">
                                    <h4 class="font-medium text-primary truncate" x-text="item.title"></h4>
                                    <p class="text-sm text-secondary truncate" x-text="item.artist"></p>
                                </div>
                                
                                <div class="text-xs text-secondary" x-text="formatTime(item.playedAt)"></div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- 音乐偏好分析 -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- 听歌时长统计 -->
                    <div>
                        <h3 class="title-md text-primary mb-4">本周听歌统计</h3>
                        <div class="chart-container">
                            <canvas id="listeningChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                    
                    <!-- 音乐偏好标签 -->
                    <div>
                        <h3 class="title-md text-primary mb-4">音乐偏好</h3>
                        <div class="bg-card-bg rounded-xl border border-border-color p-6 backdrop-filter backdrop-blur-lg">
                            <div class="flex flex-wrap">
                                <template x-for="tag in musicPreferences" :key="tag.name">
                                    <span :class="tag.active ? 'active' : ''" 
                                          class="preference-tag"
                                          x-text="tag.name"></span>
                                </template>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function profileApp() {
            return {
                userInfo: {
                    name: '音乐爱好者',
                    bio: '在音乐的汀边寻找心灵的停泊',
                    avatar: null,
                    joinDate: '2024年1月',
                    totalListeningTime: 125400 // 秒
                },
                
                userStats: {
                    favorites: 156,
                    albums: 23,
                    playlists: 8,
                    artists: 45
                },
                
                recentPlayed: [
                    {
                        id: 1,
                        title: '水调歌头',
                        artist: '古风音乐',
                        cover: null,
                        playedAt: Date.now() - 1800000 // 30分钟前
                    },
                    {
                        id: 2,
                        title: '汀上白沙',
                        artist: '民谣歌手',
                        cover: null,
                        playedAt: Date.now() - 3600000 // 1小时前
                    },
                    {
                        id: 3,
                        title: '静夜思',
                        artist: '古典音乐',
                        cover: null,
                        playedAt: Date.now() - 7200000 // 2小时前
                    }
                ],
                
                musicPreferences: [
                    { name: '古风', active: true },
                    { name: '民谣', active: true },
                    { name: '古典', active: false },
                    { name: '流行', active: false },
                    { name: '摇滚', active: false },
                    { name: '爵士', active: true },
                    { name: '电子', active: false },
                    { name: '说唱', active: false }
                ],
                
                formatDuration(seconds) {
                    const hours = Math.floor(seconds / 3600);
                    return `${hours} 小时`;
                },
                
                formatTime(timestamp) {
                    const now = Date.now();
                    const diff = now - timestamp;
                    const minutes = Math.floor(diff / 60000);
                    const hours = Math.floor(diff / 3600000);
                    const days = Math.floor(diff / 86400000);
                    
                    if (days > 0) return `${days}天前`;
                    if (hours > 0) return `${hours}小时前`;
                    if (minutes > 0) return `${minutes}分钟前`;
                    return '刚刚';
                },
                
                playSong(song) {
                    console.log('播放歌曲:', song.title);
                    // 跳转到播放器页面
                    window.location.href = './player.html';
                },
                
                editProfile() {
                    console.log('编辑个人资料');
                    // 打开编辑资料模态框
                },
                
                viewAllHistory() {
                    console.log('查看全部播放历史');
                    // 跳转到播放历史页面
                },
                
                goBack() {
                    window.history.back();
                },
                
                openSettings() {
                    window.location.href = './settings.html';
                },
                
                getChartColor(colorVar, alpha = 1) {
                    // 获取CSS变量的颜色值
                    const color = getComputedStyle(document.documentElement).getPropertyValue(`--color-${colorVar}`).trim();
                    if (color) {
                        // 如果是hex颜色，转换为rgba
                        if (color.startsWith('#')) {
                            const r = parseInt(color.slice(1, 3), 16);
                            const g = parseInt(color.slice(3, 5), 16);
                            const b = parseInt(color.slice(5, 7), 16);
                            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
                        }
                        // 如果已经是rgb/rgba格式，直接返回或修改透明度
                        if (color.startsWith('rgb')) {
                            if (alpha !== 1) {
                                const rgbMatch = color.match(/\d+/g);
                                if (rgbMatch && rgbMatch.length >= 3) {
                                    return `rgba(${rgbMatch[0]}, ${rgbMatch[1]}, ${rgbMatch[2]}, ${alpha})`;
                                }
                            }
                            return color;
                        }
                    }

                    // 备用颜色
                    const fallbackColors = {
                        'primary': alpha !== 1 ? `rgba(74, 144, 226, ${alpha})` : '#4A90E2',
                        'accent': alpha !== 1 ? `rgba(255, 193, 7, ${alpha})` : '#FFC107',
                        'card-bg': alpha !== 1 ? `rgba(255, 255, 255, ${alpha})` : '#ffffff'
                    };

                    return fallbackColors[colorVar] || (alpha !== 1 ? `rgba(74, 144, 226, ${alpha})` : '#4A90E2');
                },

                initChart() {
                    const ctx = document.getElementById('listeningChart').getContext('2d');
                    new Chart(ctx, {
                        type: 'line',
                        data: {
                            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                            datasets: [{
                                label: '听歌时长(小时)',
                                data: [2.5, 3.2, 1.8, 4.1, 2.9, 5.2, 3.7],
                                borderColor: this.getChartColor('primary'),
                                backgroundColor: this.getChartColor('primary', 0.1),
                                tension: 0.4,
                                fill: true
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                y: {
                                    beginAtZero: true,
                                    grid: {
                                        color: 'var(--border-color)'
                                    },
                                    ticks: {
                                        color: 'var(--text-secondary)'
                                    }
                                },
                                x: {
                                    grid: {
                                        color: 'var(--border-color)'
                                    },
                                    ticks: {
                                        color: 'var(--text-secondary)'
                                    }
                                }
                            }
                        }
                    });
                },
                
                init() {
                    console.log('👤 个人页面已加载');
                    
                    // 初始化图表
                    this.$nextTick(() => {
                        this.initChart();
                    });
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 个人页面');
        });
    </script>
</body>
</html>
