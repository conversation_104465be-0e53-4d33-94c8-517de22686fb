<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>专辑画卷 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .album-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .album-cover-large {
            width: 280px;
            height: 280px;
            border-radius: 20px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            flex-shrink: 0;
        }

        .album-cover-large::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }

        .album-info-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2.5rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-left: 2rem;
            flex: 1;
            min-height: 280px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .track-item {
            display: flex;
            align-items: center;
            padding: 1rem;
            border-radius: 12px;
            transition: all 0.2s ease;
            cursor: pointer;
            border-bottom: 1px solid var(--border-color);
        }
        
        .track-item:hover {
            background: var(--bg-secondary);
            transform: translateX(4px);
        }
        
        .track-item:last-child {
            border-bottom: none;
        }
        
        .track-number {
            width: 32px;
            height: 32px;
            border-radius: 8px;
            background: var(--bg-secondary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.875rem;
            color: var(--text-secondary);
            margin-right: 1rem;
            flex-shrink: 0;
        }
        
        .track-item.playing .track-number {
            background: var(--color-primary);
            color: white;
        }
        
        .album-actions {
            display: flex;
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .action-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .action-button.primary {
            background: var(--color-primary);
            color: white;
        }
        
        .action-button.primary:hover {
            background: var(--color-accent);
            transform: translateY(-2px);
        }
        
        .action-button.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .action-button.secondary:hover {
            background: var(--card-bg);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="album-container water-bg" x-data="albumApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">专辑画卷</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="shareAlbum()" class="header-button" aria-label="分享专辑">
                        <iconify-icon icon="material-symbols:share" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 专辑信息区域 -->
        <div class="px-6 py-8">
            <div class="max-w-6xl mx-auto">
                <div class="flex flex-col lg:flex-row items-start gap-8 mb-8">
                    <!-- 专辑封面 -->
                    <div class="album-cover-large mx-auto lg:mx-0">
                        <iconify-icon icon="material-symbols:album" x-show="!albumInfo.cover"></iconify-icon>
                        <img :src="albumInfo.cover" :alt="albumInfo.title" class="w-full h-full object-cover rounded-2xl" x-show="albumInfo.cover">
                    </div>

                    <!-- 专辑信息 -->
                    <div class="album-info-card w-full">
                        <div class="mb-6">
                            <h2 class="text-3xl font-bold text-primary mb-3" x-text="albumInfo.title"></h2>
                            <p class="text-xl text-secondary mb-4" x-text="albumInfo.artist"></p>

                            <div class="grid grid-cols-2 gap-4 text-sm text-secondary mb-6">
                                <div class="flex items-center">
                                    <iconify-icon icon="material-symbols:calendar-today" class="mr-2 text-accent"></iconify-icon>
                                    <span class="font-medium">发行年份：</span>
                                    <span class="ml-1" x-text="albumInfo.year"></span>
                                </div>
                                <div class="flex items-center">
                                    <iconify-icon icon="material-symbols:music-note" class="mr-2 text-accent"></iconify-icon>
                                    <span class="font-medium">歌曲数量：</span>
                                    <span class="ml-1" x-text="albumInfo.trackCount + ' 首'"></span>
                                </div>
                                <div class="flex items-center">
                                    <iconify-icon icon="material-symbols:schedule" class="mr-2 text-accent"></iconify-icon>
                                    <span class="font-medium">总时长：</span>
                                    <span class="ml-1" x-text="formatDuration(albumInfo.duration)"></span>
                                </div>
                                <div class="flex items-center">
                                    <iconify-icon icon="material-symbols:style" class="mr-2 text-accent"></iconify-icon>
                                    <span class="font-medium">风格：</span>
                                    <span class="ml-1" x-text="albumInfo.genre"></span>
                                </div>
                            </div>

                            <p class="text-secondary text-sm leading-relaxed" x-text="albumInfo.description"></p>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="album-actions">
                            <button @click="playAlbum()" class="action-button primary">
                                <iconify-icon icon="material-symbols:play-arrow" class="text-lg"></iconify-icon>
                                播放
                            </button>

                            <button @click="toggleFavorite()" class="action-button secondary">
                                <iconify-icon :icon="albumInfo.isFavorite ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'" class="text-lg"></iconify-icon>
                                <span x-text="albumInfo.isFavorite ? '已收藏' : '收藏'"></span>
                            </button>

                            <button @click="addToPlaylist()" class="action-button secondary">
                                <iconify-icon icon="material-symbols:playlist-add" class="text-lg"></iconify-icon>
                                列表
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- 歌曲列表 -->
                <div class="bg-card-bg rounded-2xl border border-border-color backdrop-filter backdrop-blur-lg overflow-hidden">
                    <div class="p-6 border-b border-border-color bg-gradient-to-r from-primary/5 to-accent/5">
                        <div class="flex items-center justify-between">
                            <h3 class="text-xl font-semibold text-primary flex items-center">
                                <iconify-icon icon="material-symbols:queue-music" class="mr-2 text-accent"></iconify-icon>
                                专辑歌曲
                            </h3>
                            <span class="text-sm text-secondary" x-text="albumTracks.length + ' 首歌曲'"></span>
                        </div>
                    </div>

                    <div class="divide-y divide-border-color">
                        <template x-for="(track, index) in albumTracks" :key="track.id">
                            <div @click="playTrack(track, index)"
                                 class="track-item p-4 hover:bg-bg-secondary/50 cursor-pointer transition-all duration-200"
                                 :class="currentTrack?.id === track.id ? 'bg-primary/10 border-l-4 border-primary' : ''">
                                <div class="flex items-center space-x-4">
                                    <!-- 曲目编号/播放状态 -->
                                    <div class="w-8 h-8 rounded-lg bg-bg-secondary flex items-center justify-center text-sm font-medium flex-shrink-0"
                                         :class="currentTrack?.id === track.id ? 'bg-primary text-white' : 'text-secondary'">
                                        <span x-show="currentTrack?.id !== track.id" x-text="index + 1"></span>
                                        <iconify-icon x-show="currentTrack?.id === track.id && isPlaying"
                                                      icon="material-symbols:graphic-eq"
                                                      class="text-sm animate-pulse"></iconify-icon>
                                        <iconify-icon x-show="currentTrack?.id === track.id && !isPlaying"
                                                      icon="material-symbols:pause"
                                                      class="text-sm"></iconify-icon>
                                    </div>

                                    <!-- 歌曲信息 -->
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-semibold text-primary truncate mb-1" x-text="track.title"></h4>
                                        <p class="text-sm text-secondary truncate" x-text="track.artist"></p>
                                    </div>

                                    <!-- 时长和操作 -->
                                    <div class="flex items-center space-x-3 flex-shrink-0">
                                        <span class="text-sm text-secondary font-mono" x-text="formatTime(track.duration)"></span>
                                        <button @click.stop="toggleTrackFavorite(track)"
                                                class="p-2 rounded-lg hover:bg-bg-secondary transition-colors">
                                            <iconify-icon :icon="track.isFavorite ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'"
                                                          :class="track.isFavorite ? 'text-accent' : 'text-secondary hover:text-accent'"
                                                          class="text-lg transition-colors"></iconify-icon>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function albumApp() {
            return {
                albumInfo: {
                    title: '诗词歌赋',
                    artist: '古风音乐',
                    year: 2024,
                    trackCount: 12,
                    duration: 2847, // 秒
                    genre: '古风/民族',
                    description: '这是一张融合了传统诗词与现代音乐的专辑，每一首歌都如同一幅水墨画，诉说着古典文化的韵味与现代情感的交融。',
                    cover: null,
                    isFavorite: false
                },
                
                albumTracks: [
                    { id: 1, title: '水调歌头', artist: '古风音乐', duration: 245, isFavorite: true },
                    { id: 2, title: '静夜思', artist: '古风音乐', duration: 198, isFavorite: false },
                    { id: 3, title: '春江花月夜', artist: '古风音乐', duration: 312, isFavorite: true },
                    { id: 4, title: '将进酒', artist: '古风音乐', duration: 267, isFavorite: false },
                    { id: 5, title: '蒹葭', artist: '古风音乐', duration: 223, isFavorite: false }
                ],
                
                currentTrack: null,
                isPlaying: false,
                
                playAlbum() {
                    if (this.albumTracks.length > 0) {
                        this.playTrack(this.albumTracks[0], 0);
                    }
                },
                
                playTrack(track, index) {
                    this.currentTrack = track;
                    this.isPlaying = true;
                    console.log('🎵 播放歌曲:', track.title);
                },
                
                toggleFavorite() {
                    this.albumInfo.isFavorite = !this.albumInfo.isFavorite;
                    console.log(this.albumInfo.isFavorite ? '⚓ 专辑已收藏' : '○ 取消收藏专辑');
                },
                
                toggleTrackFavorite(track) {
                    track.isFavorite = !track.isFavorite;
                    console.log(track.isFavorite ? '⚓ 歌曲已收藏' : '○ 取消收藏歌曲', track.title);
                },
                
                addToPlaylist() {
                    console.log('📝 添加专辑到播放列表');
                },
                
                shareAlbum() {
                    console.log('📤 分享专辑:', this.albumInfo.title);
                },
                
                formatTime(seconds) {
                    const mins = Math.floor(seconds / 60);
                    const secs = Math.floor(seconds % 60);
                    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                },
                
                formatDuration(seconds) {
                    const hours = Math.floor(seconds / 3600);
                    const mins = Math.floor((seconds % 3600) / 60);
                    if (hours > 0) {
                        return `${hours}小时${mins}分钟`;
                    }
                    return `${mins}分钟`;
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('💿 专辑页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 专辑页面');
        });
    </script>
</body>
</html>
