<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帮助与反馈 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .help-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .help-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .help-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .search-box {
            position: relative;
            margin-bottom: 2rem;
        }
        
        .search-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 2px solid var(--border-color);
            border-radius: 16px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.1);
        }
        
        .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-secondary);
        }
        
        .category-tabs {
            display: flex;
            gap: 0.5rem;
            margin-bottom: 2rem;
            overflow-x: auto;
            padding-bottom: 0.5rem;
        }
        
        .category-tab {
            padding: 0.75rem 1.5rem;
            border-radius: 20px;
            background: var(--bg-secondary);
            color: var(--text-secondary);
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
            white-space: nowrap;
            flex-shrink: 0;
        }
        
        .category-tab.active {
            background: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }
        
        .faq-item {
            border: 1px solid var(--border-color);
            border-radius: 12px;
            margin-bottom: 1rem;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .faq-question {
            padding: 1.5rem;
            background: var(--bg-secondary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.3s ease;
        }
        
        .faq-question:hover {
            background: var(--card-bg);
        }
        
        .faq-question.active {
            background: var(--card-bg);
            border-bottom: 1px solid var(--border-color);
        }
        
        .faq-answer {
            padding: 1.5rem;
            background: var(--card-bg);
            color: var(--text-secondary);
            line-height: 1.6;
            display: none;
        }
        
        .faq-answer.show {
            display: block;
        }
        
        .feedback-form {
            display: grid;
            gap: 1.5rem;
        }
        
        .form-group {
            display: flex;
            flex-direction: column;
        }
        
        .form-label {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .form-input, .form-select, .form-textarea {
            padding: 1rem;
            border: 2px solid var(--border-color);
            border-radius: 12px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            transition: all 0.3s ease;
        }
        
        .form-input:focus, .form-select:focus, .form-textarea:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 4px rgba(74, 144, 226, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 120px;
        }
        
        .submit-button {
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
        }
        
        .contact-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .contact-option {
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: var(--bg-secondary);
        }
        
        .contact-option:hover {
            border-color: var(--color-primary);
            transform: translateY(-2px);
        }
        
        .contact-icon {
            font-size: 2rem;
            color: var(--color-primary);
            margin-bottom: 1rem;
        }
        
        .contact-title {
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .contact-description {
            color: var(--text-secondary);
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="help-container water-bg" x-data="helpApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">帮助与反馈</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="clearSearch()" class="header-button" aria-label="清除搜索">
                        <iconify-icon icon="material-symbols:clear" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 帮助内容 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 搜索框 -->
                <div class="help-card">
                    <div class="search-box">
                        <iconify-icon icon="material-symbols:search" class="search-icon text-lg"></iconify-icon>
                        <input type="text" 
                               x-model="searchQuery" 
                               @input="searchFAQ()"
                               class="search-input" 
                               placeholder="搜索常见问题...">
                    </div>
                    
                    <!-- 分类标签 -->
                    <div class="category-tabs">
                        <template x-for="category in categories" :key="category.id">
                            <button @click="selectCategory(category.id)" 
                                    class="category-tab"
                                    :class="selectedCategory === category.id ? 'active' : ''"
                                    x-text="category.name">
                            </button>
                        </template>
                    </div>
                </div>
                
                <!-- 常见问题 -->
                <div class="help-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">常见问题</h2>
                    
                    <div x-show="filteredFAQs.length > 0">
                        <template x-for="faq in filteredFAQs" :key="faq.id">
                            <div class="faq-item">
                                <div @click="toggleFAQ(faq.id)" 
                                     class="faq-question"
                                     :class="openFAQs.includes(faq.id) ? 'active' : ''">
                                    <span class="font-medium text-primary" x-text="faq.question"></span>
                                    <iconify-icon :icon="openFAQs.includes(faq.id) ? 'material-symbols:expand-less' : 'material-symbols:expand-more'" 
                                                  class="text-lg text-secondary"></iconify-icon>
                                </div>
                                <div class="faq-answer" :class="openFAQs.includes(faq.id) ? 'show' : ''" x-text="faq.answer"></div>
                            </div>
                        </template>
                    </div>
                    
                    <div x-show="filteredFAQs.length === 0" class="text-center py-8">
                        <iconify-icon icon="material-symbols:search-off" class="text-4xl text-secondary opacity-50 mb-4"></iconify-icon>
                        <h3 class="text-lg font-semibold text-secondary mb-2">未找到相关问题</h3>
                        <p class="text-secondary">尝试使用其他关键词搜索，或联系我们获取帮助</p>
                    </div>
                </div>
                
                <!-- 反馈表单 -->
                <div class="help-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">问题反馈</h2>
                    
                    <form @submit.prevent="submitFeedback()" class="feedback-form">
                        <div class="form-group">
                            <label class="form-label">问题类型</label>
                            <select x-model="feedback.type" class="form-select" required>
                                <option value="">请选择问题类型</option>
                                <option value="bug">Bug报告</option>
                                <option value="feature">功能建议</option>
                                <option value="usage">使用问题</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">问题标题</label>
                            <input type="text" x-model="feedback.title" class="form-input" placeholder="简要描述你的问题" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">详细描述</label>
                            <textarea x-model="feedback.description" class="form-textarea" placeholder="请详细描述你遇到的问题或建议..." required></textarea>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">联系邮箱（可选）</label>
                            <input type="email" x-model="feedback.email" class="form-input" placeholder="如需回复，请留下邮箱">
                        </div>
                        
                        <button type="submit" class="submit-button" :disabled="isSubmitting">
                            <span x-text="isSubmitting ? '提交中...' : '提交反馈'"></span>
                        </button>
                    </form>
                </div>
                
                <!-- 联系方式 -->
                <div class="help-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">联系我们</h2>
                    
                    <div class="contact-options">
                        <div @click="contactEmail()" class="contact-option">
                            <iconify-icon icon="material-symbols:email" class="contact-icon"></iconify-icon>
                            <div class="contact-title">邮件支持</div>
                            <div class="contact-description"><EMAIL></div>
                        </div>
                        
                        <div @click="contactWeChat()" class="contact-option">
                            <iconify-icon icon="material-symbols:wechat" class="contact-icon"></iconify-icon>
                            <div class="contact-title">微信客服</div>
                            <div class="contact-description">工作日 9:00-18:00</div>
                        </div>
                        
                        <div @click="contactForum()" class="contact-option">
                            <iconify-icon icon="material-symbols:forum" class="contact-icon"></iconify-icon>
                            <div class="contact-title">用户论坛</div>
                            <div class="contact-description">社区讨论交流</div>
                        </div>
                        
                        <div @click="contactFAQ()" class="contact-option">
                            <iconify-icon icon="material-symbols:help" class="contact-icon"></iconify-icon>
                            <div class="contact-title">在线帮助</div>
                            <div class="contact-description">常见问题解答</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function helpApp() {
            return {
                searchQuery: '',
                selectedCategory: 'all',
                openFAQs: [],
                isSubmitting: false,
                
                categories: [
                    { id: 'all', name: '全部' },
                    { id: 'basic', name: '基础使用' },
                    { id: 'playlist', name: '播放列表' },
                    { id: 'settings', name: '设置' },
                    { id: 'technical', name: '技术问题' }
                ],
                
                faqs: [
                    {
                        id: 1,
                        category: 'basic',
                        question: '如何添加音乐到听汀？',
                        answer: '听汀支持本地音乐文件播放。您可以通过文件管理器将音乐文件复制到设备存储中，听汀会自动扫描并添加到音乐库。'
                    },
                    {
                        id: 2,
                        category: 'playlist',
                        question: '如何创建播放列表？',
                        answer: '在音乐库页面点击"创建播放列表"按钮，输入名称和描述，然后可以添加喜欢的歌曲到播放列表中。'
                    },
                    {
                        id: 3,
                        category: 'settings',
                        question: '如何更改主题？',
                        answer: '在设置页面中找到"外观设置"，可以选择不同的主题模式，包括浅色、深色和自动模式。'
                    },
                    {
                        id: 4,
                        category: 'technical',
                        question: '支持哪些音频格式？',
                        answer: '听汀支持常见的音频格式，包括MP3、FLAC、AAC、OGG、WAV等。如果遇到不支持的格式，建议转换为MP3格式。'
                    },
                    {
                        id: 5,
                        category: 'basic',
                        question: '如何使用均衡器？',
                        answer: '在播放界面点击均衡器按钮，可以调节不同频段的音效。我们提供了多种预设模式，也可以自定义调节。'
                    }
                ],
                
                feedback: {
                    type: '',
                    title: '',
                    description: '',
                    email: ''
                },
                
                get filteredFAQs() {
                    let filtered = this.faqs;
                    
                    // 按分类筛选
                    if (this.selectedCategory !== 'all') {
                        filtered = filtered.filter(faq => faq.category === this.selectedCategory);
                    }
                    
                    // 按搜索词筛选
                    if (this.searchQuery) {
                        const query = this.searchQuery.toLowerCase();
                        filtered = filtered.filter(faq => 
                            faq.question.toLowerCase().includes(query) || 
                            faq.answer.toLowerCase().includes(query)
                        );
                    }
                    
                    return filtered;
                },
                
                selectCategory(categoryId) {
                    this.selectedCategory = categoryId;
                    this.openFAQs = [];
                },
                
                toggleFAQ(faqId) {
                    const index = this.openFAQs.indexOf(faqId);
                    if (index > -1) {
                        this.openFAQs.splice(index, 1);
                    } else {
                        this.openFAQs.push(faqId);
                    }
                },
                
                searchFAQ() {
                    this.openFAQs = [];
                },
                
                clearSearch() {
                    this.searchQuery = '';
                    this.selectedCategory = 'all';
                    this.openFAQs = [];
                },
                
                async submitFeedback() {
                    this.isSubmitting = true;
                    
                    try {
                        // 模拟提交
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        console.log('📝 提交反馈:', this.feedback);
                        
                        // 重置表单
                        this.feedback = {
                            type: '',
                            title: '',
                            description: '',
                            email: ''
                        };
                        
                        alert('反馈提交成功！我们会尽快处理您的问题。');
                        
                    } catch (error) {
                        console.error('提交失败:', error);
                        alert('提交失败，请稍后重试。');
                    } finally {
                        this.isSubmitting = false;
                    }
                },
                
                contactEmail() {
                    window.open('mailto:<EMAIL>');
                },
                
                contactWeChat() {
                    console.log('💬 联系微信客服');
                },
                
                contactForum() {
                    console.log('🌐 打开用户论坛');
                },
                
                contactFAQ() {
                    this.selectedCategory = 'all';
                    this.searchQuery = '';
                    document.querySelector('.help-card').scrollIntoView({ behavior: 'smooth' });
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('❓ 帮助与反馈页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 帮助与反馈');
        });
    </script>
</body>
</html>
