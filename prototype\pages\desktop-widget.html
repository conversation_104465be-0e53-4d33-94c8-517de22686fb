<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>桌面小组件 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .widget-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .widget-preview {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .widget-preview:hover {
            transform: translateY(-4px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }
        
        .widget-preview::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .widget-small {
            width: 160px;
            height: 80px;
            padding: 1rem;
        }
        
        .widget-medium {
            width: 320px;
            height: 160px;
            padding: 1.5rem;
        }
        
        .widget-large {
            width: 320px;
            height: 240px;
            padding: 2rem;
        }
        
        .widget-controls {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-top: 1rem;
        }
        
        .widget-control-btn {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-primary);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .widget-control-btn:hover {
            background: var(--color-primary);
            color: white;
            transform: scale(1.1);
        }
        
        .widget-control-btn.playing {
            background: var(--color-primary);
            color: white;
        }
        
        .widget-progress {
            width: 100%;
            height: 3px;
            background: var(--bg-secondary);
            border-radius: 2px;
            overflow: hidden;
            margin-top: 1rem;
        }
        
        .widget-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        
        .widget-info {
            margin-bottom: 1rem;
        }
        
        .widget-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .widget-artist {
            font-size: 0.75rem;
            color: var(--text-secondary);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .widget-cover {
            width: 48px;
            height: 48px;
            border-radius: 8px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            flex-shrink: 0;
            margin-right: 1rem;
        }
        
        .widget-large .widget-cover {
            width: 80px;
            height: 80px;
            font-size: 2rem;
            margin-bottom: 1rem;
            margin-right: 0;
        }
        
        .config-section {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
        }
        
        .config-option {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .config-option:last-child {
            border-bottom: none;
        }
        
        .toggle-switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: var(--bg-secondary);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .toggle-switch.active {
            background: var(--color-primary);
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(24px);
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="widget-container water-bg" x-data="widgetApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">桌面小组件</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="saveSettings()" class="header-button" aria-label="保存设置">
                        <iconify-icon icon="material-symbols:save" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 小组件内容 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 小组件预览 -->
                <div class="config-section">
                    <h2 class="text-xl font-semibold text-primary mb-6">小组件预览</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <!-- 小尺寸 Widget -->
                        <div class="text-center">
                            <h3 class="text-lg font-medium text-primary mb-4">小尺寸 (2×1)</h3>
                            <div class="widget-preview widget-small mx-auto">
                                <div class="flex items-center h-full">
                                    <div class="widget-cover">
                                        <iconify-icon icon="material-symbols:music-note"></iconify-icon>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="widget-title" x-text="currentSong.title"></div>
                                        <div class="widget-artist" x-text="currentSong.artist"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 中尺寸 Widget -->
                        <div class="text-center">
                            <h3 class="text-lg font-medium text-primary mb-4">中尺寸 (4×2)</h3>
                            <div class="widget-preview widget-medium mx-auto">
                                <div class="flex items-start">
                                    <div class="widget-cover">
                                        <iconify-icon icon="material-symbols:music-note"></iconify-icon>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <div class="widget-info">
                                            <div class="widget-title" x-text="currentSong.title"></div>
                                            <div class="widget-artist" x-text="currentSong.artist"></div>
                                        </div>
                                        <div class="widget-controls">
                                            <button class="widget-control-btn" @click="previousSong()">
                                                <iconify-icon icon="material-symbols:skip-previous" class="text-sm"></iconify-icon>
                                            </button>
                                            <button class="widget-control-btn" :class="isPlaying ? 'playing' : ''" @click="togglePlay()">
                                                <iconify-icon :icon="isPlaying ? 'material-symbols:pause' : 'material-symbols:play-arrow'" class="text-sm"></iconify-icon>
                                            </button>
                                            <button class="widget-control-btn" @click="nextSong()">
                                                <iconify-icon icon="material-symbols:skip-next" class="text-sm"></iconify-icon>
                                            </button>
                                        </div>
                                        <div class="widget-progress">
                                            <div class="widget-progress-bar" :style="`width: ${progress}%`"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 大尺寸 Widget -->
                        <div class="text-center">
                            <h3 class="text-lg font-medium text-primary mb-4">大尺寸 (4×3)</h3>
                            <div class="widget-preview widget-large mx-auto">
                                <div class="text-center">
                                    <div class="widget-cover mx-auto">
                                        <iconify-icon icon="material-symbols:music-note"></iconify-icon>
                                    </div>
                                    <div class="widget-info">
                                        <div class="widget-title" x-text="currentSong.title"></div>
                                        <div class="widget-artist" x-text="currentSong.artist"></div>
                                    </div>
                                    <div class="widget-controls justify-center">
                                        <button class="widget-control-btn" @click="previousSong()">
                                            <iconify-icon icon="material-symbols:skip-previous"></iconify-icon>
                                        </button>
                                        <button class="widget-control-btn" :class="isPlaying ? 'playing' : ''" @click="togglePlay()">
                                            <iconify-icon :icon="isPlaying ? 'material-symbols:pause' : 'material-symbols:play-arrow'"></iconify-icon>
                                        </button>
                                        <button class="widget-control-btn" @click="nextSong()">
                                            <iconify-icon icon="material-symbols:skip-next"></iconify-icon>
                                        </button>
                                    </div>
                                    <div class="widget-progress">
                                        <div class="widget-progress-bar" :style="`width: ${progress}%`"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 小组件设置 -->
                <div class="config-section">
                    <h2 class="text-xl font-semibold text-primary mb-6">小组件设置</h2>
                    
                    <div class="config-option">
                        <div>
                            <h3 class="font-medium text-primary">显示专辑封面</h3>
                            <p class="text-sm text-secondary">在小组件中显示当前歌曲的专辑封面</p>
                        </div>
                        <div class="toggle-switch" :class="settings.showCover ? 'active' : ''" @click="settings.showCover = !settings.showCover">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="config-option">
                        <div>
                            <h3 class="font-medium text-primary">显示播放控制</h3>
                            <p class="text-sm text-secondary">在小组件中显示播放控制按钮</p>
                        </div>
                        <div class="toggle-switch" :class="settings.showControls ? 'active' : ''" @click="settings.showControls = !settings.showControls">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="config-option">
                        <div>
                            <h3 class="font-medium text-primary">显示播放进度</h3>
                            <p class="text-sm text-secondary">在小组件中显示当前播放进度</p>
                        </div>
                        <div class="toggle-switch" :class="settings.showProgress ? 'active' : ''" @click="settings.showProgress = !settings.showProgress">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                    
                    <div class="config-option">
                        <div>
                            <h3 class="font-medium text-primary">自动更新</h3>
                            <p class="text-sm text-secondary">自动同步播放状态和歌曲信息</p>
                        </div>
                        <div class="toggle-switch" :class="settings.autoUpdate ? 'active' : ''" @click="settings.autoUpdate = !settings.autoUpdate">
                            <div class="toggle-slider"></div>
                        </div>
                    </div>
                </div>
                
                <!-- 使用说明 -->
                <div class="config-section">
                    <h2 class="text-xl font-semibold text-primary mb-6">使用说明</h2>
                    
                    <div class="space-y-4 text-secondary">
                        <div class="flex items-start space-x-3">
                            <iconify-icon icon="material-symbols:info" class="text-accent mt-1"></iconify-icon>
                            <div>
                                <h4 class="font-medium text-primary">添加到桌面</h4>
                                <p class="text-sm">长按桌面空白处，选择"小组件"，找到"听汀"并添加到桌面</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <iconify-icon icon="material-symbols:touch-app" class="text-accent mt-1"></iconify-icon>
                            <div>
                                <h4 class="font-medium text-primary">快速控制</h4>
                                <p class="text-sm">点击小组件上的控制按钮可以直接控制音乐播放</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <iconify-icon icon="material-symbols:open-in-new" class="text-accent mt-1"></iconify-icon>
                            <div>
                                <h4 class="font-medium text-primary">打开应用</h4>
                                <p class="text-sm">点击歌曲信息区域可以快速打开听汀应用</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function widgetApp() {
            return {
                isPlaying: false,
                progress: 35,
                
                currentSong: {
                    title: '水调歌头',
                    artist: '古风音乐'
                },
                
                settings: {
                    showCover: true,
                    showControls: true,
                    showProgress: true,
                    autoUpdate: true
                },
                
                togglePlay() {
                    this.isPlaying = !this.isPlaying;
                    console.log(this.isPlaying ? '▶️ 开始播放' : '⏸️ 暂停播放');
                },
                
                previousSong() {
                    console.log('⏮️ 上一首');
                    this.currentSong = {
                        title: '静夜思',
                        artist: '古典音乐'
                    };
                },
                
                nextSong() {
                    console.log('⏭️ 下一首');
                    this.currentSong = {
                        title: '春江花月夜',
                        artist: '古风音乐'
                    };
                },
                
                saveSettings() {
                    console.log('💾 保存小组件设置', this.settings);
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('🎛️ 桌面小组件页面已加载');
                    
                    // 模拟播放进度
                    setInterval(() => {
                        if (this.isPlaying) {
                            this.progress = (this.progress + 1) % 100;
                        }
                    }, 1000);
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 桌面小组件');
        });
    </script>
</body>
</html>
