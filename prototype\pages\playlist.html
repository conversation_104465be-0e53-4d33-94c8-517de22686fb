<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>音乐册页 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .playlist-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .playlist-cover {
            width: 200px;
            height: 200px;
            border-radius: 20px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            box-shadow: 0 20px 60px rgba(74, 144, 226, 0.3);
            position: relative;
            overflow: hidden;
            margin: 0 auto;
            cursor: pointer;
        }
        
        .playlist-cover::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .playlist-info-card {
            background: var(--card-bg);
            border-radius: 24px;
            padding: 2.5rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-top: 2rem;
            text-align: center;
        }
        
        .song-item-draggable {
            background: var(--card-bg);
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            cursor: grab;
            backdrop-filter: blur(10px);
            margin-bottom: 0.5rem;
        }
        
        .song-item-draggable:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
        }
        
        .song-item-draggable:active {
            cursor: grabbing;
            transform: rotate(2deg);
        }
        
        .song-item-draggable.dragging {
            opacity: 0.5;
            transform: rotate(5deg) scale(1.05);
            z-index: 1000;
        }
        
        .drag-handle {
            color: var(--text-secondary);
            cursor: grab;
            padding: 0.5rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .drag-handle:hover {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }
        
        .playlist-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }
        
        .action-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 1.5rem;
            border-radius: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            flex: 1;
            min-width: 140px;
            justify-content: center;
        }
        
        .action-button.primary {
            background: var(--color-primary);
            color: white;
        }
        
        .action-button.primary:hover {
            background: var(--color-accent);
            transform: translateY(-2px);
        }
        
        .action-button.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .action-button.secondary:hover {
            background: var(--card-bg);
            transform: translateY(-2px);
        }
        
        .create-playlist-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            backdrop-filter: blur(4px);
        }
        
        .modal-content {
            background: var(--card-bg);
            border-radius: 24px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            max-width: 400px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            width: 100%;
            padding: 0.875rem;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: var(--color-primary);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
        }
        
        .form-textarea {
            resize: vertical;
            min-height: 80px;
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="playlist-container water-bg" x-data="playlistApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">音乐册页</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="showCreateModal = true" class="header-button" aria-label="创建播放列表">
                        <iconify-icon icon="material-symbols:add" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button @click="sharePlaylist()" class="header-button" aria-label="分享播放列表">
                        <iconify-icon icon="material-symbols:share" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 播放列表信息区域 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 播放列表封面 -->
                <div @click="changeCover()" class="playlist-cover mb-6">
                    <iconify-icon icon="material-symbols:playlist-play" x-show="!playlistInfo.cover"></iconify-icon>
                    <img :src="playlistInfo.cover" :alt="playlistInfo.name" class="w-full h-full object-cover rounded-2xl" x-show="playlistInfo.cover">
                </div>
                
                <!-- 播放列表信息卡片 -->
                <div class="playlist-info-card">
                    <h2 class="text-3xl font-bold text-primary mb-3" x-text="playlistInfo.name"></h2>
                    <p class="text-lg text-secondary mb-4" x-text="playlistInfo.description"></p>
                    
                    <div class="flex justify-center space-x-8 text-sm text-secondary mb-6">
                        <div>
                            <span class="font-medium">歌曲数量：</span>
                            <span x-text="playlistSongs.length + ' 首'"></span>
                        </div>
                        <div>
                            <span class="font-medium">总时长：</span>
                            <span x-text="formatTotalDuration()"></span>
                        </div>
                        <div>
                            <span class="font-medium">创建时间：</span>
                            <span x-text="playlistInfo.createdAt"></span>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="playlist-actions">
                        <button @click="playPlaylist()" class="action-button primary">
                            <iconify-icon icon="material-symbols:play-arrow" class="text-lg"></iconify-icon>
                            播放全部
                        </button>
                        
                        <button @click="shufflePlay()" class="action-button secondary">
                            <iconify-icon icon="material-symbols:shuffle" class="text-lg"></iconify-icon>
                            随机播放
                        </button>
                        
                        <button @click="editPlaylist()" class="action-button secondary">
                            <iconify-icon icon="material-symbols:edit" class="text-lg"></iconify-icon>
                            编辑信息
                        </button>
                    </div>
                </div>
                
                <!-- 歌曲列表 -->
                <div class="mt-8">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-primary">歌曲列表</h3>
                        <button @click="addSongs()" class="text-sm text-primary hover:text-accent transition-colors">
                            <iconify-icon icon="material-symbols:add" class="mr-1"></iconify-icon>
                            添加歌曲
                        </button>
                    </div>
                    
                    <div class="space-y-2" x-ref="songList">
                        <template x-for="(song, index) in playlistSongs" :key="song.id">
                            <div class="song-item-draggable" 
                                 draggable="true"
                                 @dragstart="dragStart($event, index)"
                                 @dragover.prevent
                                 @drop="drop($event, index)">
                                <div class="flex items-center space-x-4">
                                    <!-- 拖拽手柄 -->
                                    <div class="drag-handle">
                                        <iconify-icon icon="material-symbols:drag-indicator" class="text-lg"></iconify-icon>
                                    </div>
                                    
                                    <!-- 歌曲编号 -->
                                    <div class="w-8 text-center text-sm text-secondary font-mono" x-text="index + 1"></div>
                                    
                                    <!-- 歌曲封面 -->
                                    <div class="w-12 h-12 rounded-lg bg-gradient-to-br from-primary to-accent flex items-center justify-center text-white flex-shrink-0">
                                        <iconify-icon icon="material-symbols:music-note" class="text-lg" x-show="!song.cover"></iconify-icon>
                                        <img :src="song.cover" :alt="song.title" class="w-full h-full object-cover rounded-lg" x-show="song.cover">
                                    </div>
                                    
                                    <!-- 歌曲信息 -->
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-semibold text-primary truncate" x-text="song.title"></h4>
                                        <p class="text-sm text-secondary truncate" x-text="song.artist"></p>
                                    </div>
                                    
                                    <!-- 时长和操作 -->
                                    <div class="flex items-center space-x-3 flex-shrink-0">
                                        <span class="text-sm text-secondary font-mono" x-text="formatTime(song.duration)"></span>
                                        <button @click="removeSong(index)" class="p-2 rounded-lg hover:bg-bg-secondary transition-colors">
                                            <iconify-icon icon="material-symbols:close" class="text-lg text-secondary hover:text-error"></iconify-icon>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </template>
                    </div>
                    
                    <!-- 空状态 -->
                    <div x-show="playlistSongs.length === 0" class="text-center py-16">
                        <iconify-icon icon="material-symbols:playlist-add" class="text-6xl text-secondary opacity-50 mb-4"></iconify-icon>
                        <h3 class="text-lg font-semibold text-secondary mb-2">播放列表为空</h3>
                        <p class="text-secondary mb-4">添加一些歌曲来开始你的音乐之旅</p>
                        <button @click="addSongs()" class="action-button primary">
                            <iconify-icon icon="material-symbols:add" class="text-lg"></iconify-icon>
                            添加歌曲
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 创建播放列表模态框 -->
        <div x-show="showCreateModal" class="create-playlist-modal" @click.self="showCreateModal = false">
            <div class="modal-content">
                <h3 class="text-xl font-bold text-primary mb-6">创建播放列表</h3>
                
                <form @submit.prevent="createPlaylist()">
                    <div class="form-group">
                        <label class="form-label">播放列表名称</label>
                        <input type="text" x-model="newPlaylist.name" class="form-input" placeholder="输入播放列表名称" required>
                    </div>
                    
                    <div class="form-group">
                        <label class="form-label">描述</label>
                        <textarea x-model="newPlaylist.description" class="form-input form-textarea" placeholder="描述这个播放列表..."></textarea>
                    </div>
                    
                    <div class="flex space-x-3">
                        <button type="button" @click="showCreateModal = false" class="action-button secondary flex-1">
                            取消
                        </button>
                        <button type="submit" class="action-button primary flex-1">
                            创建
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <script>
        function playlistApp() {
            return {
                showCreateModal: false,
                draggedIndex: null,
                
                playlistInfo: {
                    name: '我的收藏',
                    description: '收藏的经典歌曲合集',
                    cover: null,
                    createdAt: '2024年1月'
                },
                
                newPlaylist: {
                    name: '',
                    description: ''
                },
                
                playlistSongs: [
                    {
                        id: 1,
                        title: '水调歌头',
                        artist: '古风音乐',
                        duration: 245,
                        cover: null
                    },
                    {
                        id: 2,
                        title: '静夜思',
                        artist: '古典音乐',
                        duration: 198,
                        cover: null
                    },
                    {
                        id: 3,
                        title: '春江花月夜',
                        artist: '古风音乐',
                        duration: 312,
                        cover: null
                    }
                ],
                
                playPlaylist() {
                    console.log('🎵 播放播放列表:', this.playlistInfo.name);
                    window.location.href = './player.html';
                },
                
                shufflePlay() {
                    console.log('🔀 随机播放播放列表');
                },
                
                editPlaylist() {
                    console.log('✏️ 编辑播放列表信息');
                },
                
                changeCover() {
                    console.log('🖼️ 更换播放列表封面');
                },
                
                addSongs() {
                    console.log('➕ 添加歌曲到播放列表');
                },
                
                removeSong(index) {
                    this.playlistSongs.splice(index, 1);
                    console.log('🗑️ 移除歌曲');
                },
                
                dragStart(event, index) {
                    this.draggedIndex = index;
                    event.target.classList.add('dragging');
                },
                
                drop(event, targetIndex) {
                    if (this.draggedIndex !== null && this.draggedIndex !== targetIndex) {
                        const draggedSong = this.playlistSongs[this.draggedIndex];
                        this.playlistSongs.splice(this.draggedIndex, 1);
                        this.playlistSongs.splice(targetIndex, 0, draggedSong);
                        console.log('🔄 歌曲顺序已调整');
                    }
                    this.draggedIndex = null;
                    document.querySelectorAll('.dragging').forEach(el => el.classList.remove('dragging'));
                },
                
                createPlaylist() {
                    console.log('📝 创建播放列表:', this.newPlaylist.name);
                    this.showCreateModal = false;
                    this.newPlaylist = { name: '', description: '' };
                },
                
                sharePlaylist() {
                    console.log('📤 分享播放列表');
                },
                
                formatTime(seconds) {
                    const mins = Math.floor(seconds / 60);
                    const secs = Math.floor(seconds % 60);
                    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                },
                
                formatTotalDuration() {
                    const total = this.playlistSongs.reduce((sum, song) => sum + song.duration, 0);
                    const hours = Math.floor(total / 3600);
                    const mins = Math.floor((total % 3600) / 60);
                    if (hours > 0) {
                        return `${hours}小时${mins}分钟`;
                    }
                    return `${mins}分钟`;
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('📋 播放列表页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 播放列表');
        });
    </script>
</body>
</html>
