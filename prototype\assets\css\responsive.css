/* 
 * 「听汀」响应式设计系统 v1.0
 * iOS/iPad 设计规范适配
 * 支持多种设备尺寸和方向
 */

/* ========== 基础响应式断点 ========== */

/* 小屏手机 (iPhone SE, iPhone 12 mini) */
@media (max-width: 374px) {
  html {
    font-size: 14px;
  }
  
  .container {
    padding: 0 1rem;
  }
  
  .btn {
    min-height: 48px;
    padding: 0.75rem 1rem;
  }
  
  .album-cover-large {
    width: 220px;
    height: 220px;
  }
  
  .harbor-illustration {
    width: 200px;
    height: 140px;
  }
}

/* 标准手机 (iPhone 12, iPhone 13) */
@media (min-width: 375px) and (max-width: 767px) {
  .container {
    padding: 0 1.5rem;
  }
  
  /* 主界面适配 */
  .current-playing-card {
    margin: 0 1rem;
  }
  
  .album-cover {
    width: 64px;
    height: 64px;
  }
  
  /* 播放器界面适配 */
  .album-cover-large {
    width: 260px;
    height: 260px;
  }
  
  .lyrics-container {
    max-height: 160px;
  }
  
  /* 歌曲列表适配 */
  .song-item {
    padding: 0.75rem;
  }
  
  .album-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }
  
  /* 设置页面适配 */
  .settings-item {
    padding: 0.75rem;
  }
  
  .theme-preview {
    width: 80px;
    height: 48px;
  }
}

/* 大屏手机 (iPhone 12 Pro Max, iPhone 13 Pro Max) */
@media (min-width: 414px) and (max-width: 767px) {
  .album-cover-large {
    width: 300px;
    height: 300px;
  }
  
  .harbor-illustration {
    width: 320px;
    height: 220px;
  }
  
  .lyrics-container {
    max-height: 180px;
  }
}

/* 小平板 (iPad mini) */
@media (min-width: 768px) and (max-width: 1023px) {
  html {
    font-size: 16px;
  }
  
  .container {
    padding: 0 2rem;
  }
  
  /* 双列布局 */
  .main-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 2rem;
  }
  
  .main-content {
    grid-column: 1;
  }
  
  .sidebar {
    grid-column: 2;
    position: sticky;
    top: 0;
    height: 100vh;
    overflow-y: auto;
  }
  
  /* 播放器界面适配 */
  .player-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
    padding: 2rem;
  }
  
  .album-cover-large {
    width: 320px;
    height: 320px;
  }
  
  /* 歌曲列表适配 */
  .album-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .song-item {
    padding: 1rem;
  }
  
  /* 设置页面适配 */
  .settings-container {
    max-width: 600px;
    margin: 0 auto;
  }
}

/* 大平板 (iPad Pro 11", iPad Air) */
@media (min-width: 1024px) and (max-width: 1365px) {
  .container {
    padding: 0 3rem;
  }
  
  /* 三列布局 */
  .main-container {
    display: grid;
    grid-template-columns: 300px 1fr 400px;
    gap: 2rem;
  }
  
  .navigation {
    grid-column: 1;
  }
  
  .main-content {
    grid-column: 2;
  }
  
  .sidebar {
    grid-column: 3;
  }
  
  /* 播放器界面适配 */
  .player-container {
    grid-template-columns: 1fr 1fr;
    max-width: 1000px;
    margin: 0 auto;
  }
  
  .album-cover-large {
    width: 360px;
    height: 360px;
  }
  
  .lyrics-container {
    max-height: 300px;
  }
  
  /* 歌曲列表适配 */
  .album-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
  }
  
  /* 设置页面适配 */
  .settings-container {
    max-width: 800px;
    margin: 0 auto;
  }
  
  .settings-card {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
}

/* 超大平板 (iPad Pro 12.9") */
@media (min-width: 1366px) {
  .container {
    padding: 0 4rem;
    max-width: 1400px;
    margin: 0 auto;
  }
  
  /* 播放器界面适配 */
  .album-cover-large {
    width: 400px;
    height: 400px;
  }
  
  /* 歌曲列表适配 */
  .album-grid {
    grid-template-columns: repeat(5, 1fr);
    gap: 2rem;
  }
}

/* ========== 横屏适配 ========== */

/* 手机横屏 */
@media (max-width: 767px) and (orientation: landscape) {
  /* 播放器界面横屏优化 */
  .player-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 1rem;
  }
  
  .player-main {
    grid-column: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .player-controls {
    grid-column: 2;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
  
  .album-cover-large {
    width: 200px;
    height: 200px;
  }
  
  .lyrics-container {
    max-height: 120px;
  }
  
  /* 隐藏不必要的元素 */
  .floating-background {
    display: none;
  }
  
  /* 空汀状态横屏适配 */
  .empty-harbor {
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
  }
  
  .harbor-illustration {
    width: 200px;
    height: 140px;
  }
}

/* 平板横屏 */
@media (min-width: 768px) and (orientation: landscape) {
  .player-container {
    grid-template-columns: 1fr 1fr;
    max-width: none;
  }
  
  .album-cover-large {
    width: 300px;
    height: 300px;
  }
}

/* ========== 触摸目标优化 ========== */

/* 移动设备触摸目标最小尺寸 */
@media (pointer: coarse) {
  .btn,
  button,
  .song-item,
  .album-card,
  .settings-item {
    min-height: 48px;
  }
  
  .control-button {
    width: 56px;
    height: 56px;
  }
  
  .play-button {
    width: 72px;
    height: 72px;
  }
  
  .anchor-icon {
    padding: 0.75rem;
  }
  
  .toggle-switch {
    width: 56px;
    height: 32px;
  }
  
  .toggle-slider:before {
    height: 24px;
    width: 24px;
    left: 4px;
    bottom: 4px;
  }
  
  input:checked + .toggle-slider:before {
    transform: translateX(24px);
  }
}

/* ========== 高分辨率屏幕适配 ========== */

/* Retina 显示屏 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .album-cover,
  .album-cover-large,
  .harbor-illustration {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* ========== 无障碍适配 ========== */

/* 减少动画 */
@media (prefers-reduced-motion: reduce) {
  .album-cover-large.playing {
    animation: none;
  }
  
  .floating-particles,
  .floating-background {
    display: none;
  }
  
  .ripple-effect::before {
    transition: none;
  }
  
  .theme-switching::before {
    animation: none;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  :root {
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.3);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  }
  
  .card,
  .settings-card,
  .current-playing-card {
    border-width: 2px;
  }
  
  .btn {
    border-width: 2px;
  }
}

/* 大字体模式 */
@media (prefers-font-size: large) {
  html {
    font-size: 18px;
  }
  
  .btn {
    min-height: 52px;
    padding: 1rem 1.5rem;
  }
}

/* ========== 打印样式 ========== */

@media print {
  .floating-particles,
  .floating-background,
  .water-bg,
  .ripple-effect,
  button,
  .control-button,
  .play-button {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .card,
  .settings-card {
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
}

/* ========== 特殊设备适配 ========== */

/* iPhone X 系列刘海屏适配 */
@supports (padding: max(0px)) {
  .header,
  .footer,
  .fixed {
    padding-left: max(1rem, env(safe-area-inset-left));
    padding-right: max(1rem, env(safe-area-inset-right));
  }
  
  .header {
    padding-top: max(1rem, env(safe-area-inset-top));
  }
  
  .footer {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

/* 折叠屏设备适配 */
@media (min-width: 768px) and (max-width: 1023px) and (orientation: portrait) {
  .main-container {
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
  }

  .sidebar {
    grid-row: 1;
    height: auto;
    position: static;
  }

  .main-content {
    grid-row: 2;
  }
}

/* ========== 新增页面响应式适配 ========== */

/* 搜索页面适配 */
@media (max-width: 767px) {
  .search-box {
    padding: 1rem 1.25rem;
  }

  .search-input {
    font-size: 1rem;
    padding-left: 3rem;
  }

  .hot-search-tag {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }
}

/* 专辑页面适配 */
@media (max-width: 1023px) {
  .album-cover-large {
    width: 200px !important;
    height: 200px !important;
    margin-bottom: 2rem;
  }

  .album-info-card {
    margin-left: 0 !important;
    margin-top: 0 !important;
  }
}

/* 艺术家页面适配 */
@media (max-width: 767px) {
  .artist-avatar {
    width: 150px !important;
    height: 150px !important;
  }

  .artist-info-card {
    padding: 2rem !important;
  }
}

/* 歌曲详情页面适配 */
@media (max-width: 767px) {
  .song-cover-large {
    width: 240px !important;
    height: 240px !important;
  }

  .tech-info-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-button {
    min-width: auto !important;
  }
}

/* 播放列表页面适配 */
@media (max-width: 767px) {
  .playlist-cover {
    width: 160px !important;
    height: 160px !important;
  }

  .playlist-info-card {
    padding: 2rem !important;
  }

  .playlist-actions {
    flex-direction: column;
  }
}

/* 个人中心页面适配 */
@media (max-width: 767px) {
  .profile-avatar {
    width: 100px !important;
    height: 100px !important;
  }

  .stats-card {
    min-height: 120px !important;
    padding: 1.5rem !important;
  }

  .quick-action {
    padding: 1rem !important;
  }
}

/* 音乐库页面适配 */
@media (max-width: 767px) {
  .category-card {
    padding: 2rem !important;
    min-height: 140px !important;
  }

  .category-icon {
    font-size: 2.5rem !important;
  }
}

/* 锚点页面适配 */
@media (max-width: 767px) {
  .anchor-cover {
    width: 60px !important;
    height: 60px !important;
    margin-right: 1rem !important;
  }

  .anchor-item {
    padding: 1.5rem !important;
  }
}

/* 均衡器页面适配 */
@media (max-width: 767px) {
  .eq-slider {
    height: 180px !important;
    width: 10px !important;
  }

  .eq-band {
    margin: 0 0.5rem !important;
    min-width: 50px !important;
  }

  .preset-card {
    padding: 0.75rem !important;
  }
}

/* 音乐空间页面适配 */
@media (max-width: 767px) {
  .radar-chart-container {
    height: 250px !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }

  .music-card {
    padding: 1rem !important;
  }

  .qr-code {
    width: 100px !important;
    height: 100px !important;
  }
}
