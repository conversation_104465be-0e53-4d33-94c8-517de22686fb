*create-doc prd

收到！我将按照2→1的顺序执行：先讨论需求优先级和MVP范围，然后创建完整PRD。

# 需求优先级讨论 & MVP范围确定

基于Mary的分析师报告，让我们先确定「听汀」的MVP核心需求：

## 需求优先级矩阵

| 需求类别 | 具体功能 | 用户价值 | 开发复杂度 | 优先级 |
|---------|---------|----------|------------|--------|
| **核心播放** | 本地音乐播放 | 极高 | 低 | P0 |
| **音乐管理** | 音乐库扫描 | 极高 | 中 | P0 |
| **基础UI** | 极简界面设计 | 高 | 中 | P0 |
| **离线核心** | 完全离线运行 | 极高 | 低 | P0 |
| **美学体验** | "汀"主题设计 | 高 | 高 | P1 |
| **情感功能** | 收藏锚点系统 | 中 | 中 | P1 |
| **高级功能** | 无损音质 | 中 | 低 | P1 |
| **社区功能** | 主题商店 | 低 | 高 | P2 |

## MVP范围确定（首版本）

**必须包含（MVP）**：
1. ✅ 本地音乐文件扫描和播放
2. ✅ 基础播放列表管理
3. ✅ 极简界面（空汀状态）
4. ✅ 完全离线运行（无网络权限）
5. ✅ 基础主题（昼夜两种）

**可以延后（后续版本）**：
1. 🔄 水波动画和高级视觉效果
2. 🔄 收藏锚点完整系统
3. 🔄 无损音质支持
4. 🔄 主题商店

## 关键决策点
- **技术栈**：Flutter（跨平台，性能优秀）
- **音频引擎**：平台原生API（确保最佳音质）
- **存储**：SQLite（轻量级，性能足够）

现在让我创建基于这个MVP范围的完整PRD：

---

# 产品需求文档 (PRD) - 「听汀」离线音乐播放器 v1.0

# 1. 产品概述

## 1.1 产品名称
**听汀 (TingTing)** - 离线音乐播放器

## 1.2 产品定位
"数字时代的私人音乐港湾" - 专注于纯粹本地音乐播放的美学产品

## 1.3 核心价值主张
- **纯粹性**：无广告、无推送、无网络依赖
- **美学性**：东方美学与现代设计的完美融合
- **情感性**：音乐收藏的情感化表达

# 2. 目标用户

## 2.1 主要用户群体
- **数字极简主义者** (35%)
- **音乐收藏爱好者** (30%)
- **专注工作者** (25%)
- **隐私意识者** (10%)

## 2.2 用户画像
```
姓名：李明
年龄：28岁
职业：软件工程师
特征：
- 拥有2TB本地音乐收藏
- 厌倦流媒体广告和推荐
- 注重隐私保护
- 愿意为优质体验付费
```

# 3. 功能需求 (MVP)

## 3.1 核心播放功能

## 3.1.1 音频格式支持
- **必须支持**：MP3, M4A, FLAC, WAV, OGG
- **支持范围**：44.1kHz-192kHz采样率
- **比特率**：支持最高320kbps及以上

## 3.1.2 播放控制
- 播放/暂停
- 上一首/下一首
- 进度条拖拽
- 随机播放
- 循环播放（单曲/列表）
- 播放速度：0.5x - 2.0x

## 3.1.3 音频质量
- 标准音质：16bit/44.1kHz
- 高品质：24bit/96kHz（如文件支持）
- 智能增益控制（防止音量突变）

## 3.2 音乐库管理

## 3.2.1 文件扫描
- 自动扫描：全盘扫描+增量扫描
- 扫描速度：1000首歌曲<30秒
- 支持文件夹：用户可选择扫描目录
- 格式过滤：可自定义扫描格式

## 3.2.2 元数据处理
- 自动获取：标题、艺术家、专辑、封面
- 手动编辑：支持修改所有元数据
- 封面显示：自动下载高清封面
- 歌词显示：支持LRC格式歌词

## 3.2.3 组织方式
- 按艺术家浏览
- 按专辑浏览
- 按文件夹浏览
- 按最近添加排序

## 3.3 播放列表系统

## 3.3.1 基础功能
- 创建/编辑/删除播放列表
- 拖拽添加歌曲
- 播放列表重命名
- 播放列表封面（自动生成）

## 3.3.2 特殊列表
- 收藏列表（心形标记）
- 最近播放
- 最常播放
- 最近添加

## 3.4 用户界面设计

## 3.4.1 主题系统
- **晨汀**：晨光微熹的淡蓝色调
- **夜汀**：月光如水的深蓝色调
- **空汀**：播放列表为空时的特殊界面

## 3.4.2 空汀状态
- 视觉：简洁的码头轮廓
- 文案："请把音乐放进你的汀"
- 引导：扫描音乐文件的按钮

## 3.4.3 主要界面
- **主界面**：底部播放控制栏 + 内容区域
- **播放界面**：大封面 + 歌词 + 进度条
- **列表界面**：简洁的列表视图

## 3.5 离线功能

## 3.5.1 完全离线
- 无需网络权限
- 无云同步功能（MVP版本）
- 所有数据本地存储

## 3.5.2 本地存储
- 音乐库：SQLite数据库
- 设置偏好：SharedPreferences
- 缓存：本地文件缓存

# 4. 非功能需求

## 4.1 性能要求
- 冷启动时间：< 2秒
- 内存占用：< 150MB
- 电池消耗：< 主流播放器平均水平
- 扫描1000首歌曲：< 30秒

## 4.2 兼容性
- **Android**：7.0+ (API 24+)
- **iOS**：12.0+
- **屏幕适配**：4.7寸 - 12.9寸
- **方向支持**：竖屏/横屏

## 4.3 可靠性
- 崩溃率：< 0.1%
- 数据丢失率：0%
- 播放稳定性：连续播放8小时无异常

# 5. 用户体验设计

## 5.1 首次使用流程
1. **欢迎界面**：品牌介绍（3秒）
2. **权限请求**：存储权限（必需）
3. **引导扫描**：选择音乐文件夹
4. **空汀状态**：展示空列表界面
5. **首次播放**：播放列表自动播放

## 5.2 交互细节
- **手势操作**：左右滑动切换标签
- **长按操作**：快速添加到收藏
- **双击操作**：播放/暂停
- **震动反馈**：关键操作的轻微震动

# 6. 数据模型

## 6.1 音乐文件
```json
{
  "id": "unique_id",
  "file_path": "/music/song.mp3",
  "title": "歌曲名称",
  "artist": "艺术家",
  "album": "专辑名称",
  "duration": 245, // 秒
  "bitrate": 320, // kbps
  "format": "MP3",
  "cover_path": "/covers/album.jpg",
  "lyrics_path": "/lyrics/song.lrc",
  "added_date": "2024-01-15",
  "play_count": 42,
  "is_favorite": true
}
```

## 6.2 播放列表
```json
{
  "id": "playlist_id",
  "name": "我的汀",
  "cover": "auto_generated",
  "songs": ["song_id_1", "song_id_2"],
  "created_date": "2024-01-15",
  "updated_date": "2024-01-20"
}
```

# 7. 技术架构建议

## 7.1 技术选型
- **跨平台**：Flutter
- **状态管理**：Provider
- **音频引擎**：audioplayers插件
- **数据库**：sqflite
- **文件操作**：path_provider

## 7.2 架构模式
- **MVVM**：Model-View-ViewModel
- **依赖注入**：手动注入
- **错误处理**：全局错误捕获

# 8. 测试策略

## 8.1 单元测试
- 音频播放逻辑
- 音乐库管理
- 播放列表操作
- 元数据处理

## 8.2 集成测试
- 完整播放流程
- 文件扫描功能
- 数据持久化

## 8.3 性能测试
- 启动时间
- 内存使用
- 电池消耗

# 9. 发布计划

## 9.1 版本1.0 (MVP)
- **时间**：3个月
- **功能**：核心播放 + 基础管理
- **平台**：Android先行，iOS后续

## 9.2 版本1.1
- **时间**：发布后2个月
- **功能**：美学升级 + 收藏系统

## 9.3 版本2.0
- **时间**：发布后6个月
- **功能**：主题商店 + 高级功能

# 10. 成功指标

## 10.1 用户指标
- 下载量：首月10,000+
- 日活：首月1,000+
- 留存：7日>40%，30日>20%

## 10.2 质量指标
- 评分：>4.5星
- 崩溃率：<0.1%
- 启动时间：<2秒

## 10.3 商业指标
- 付费转化：>3%
- 用户反馈：>80%正面

# 11. 风险与缓解

## 11.1 技术风险
- **音频格式兼容性**：全面测试
- **大库性能**：优化数据库查询
- **权限问题**：详细权限处理

## 11.2 市场风险
- **用户获取**：社区营销
- **竞品压力**：差异化定位
- **付费意愿**：免费+Pro模式

# 12. 后续规划

## 12.1 短期（6个月）
- 完善基础功能
- 优化用户体验
- 建立用户社区

## 12.2 中期（1年）
- 主题商店上线
- 高级功能开发
- 多语言支持

## 12.3 长期（2年）
- 音乐文化社区
- 艺术家合作
- 硬件生态扩展

# 13. 扩展功能模块 (v2.0) - 新增

## 13.1 搜索功能模块
### 13.1.1 核心功能
- **全局搜索**：支持歌曲、专辑、艺术家的全文搜索
- **搜索历史**：记录用户搜索历史，支持快速重复搜索
- **智能建议**：基于输入内容提供搜索建议
- **搜索结果**：分类展示搜索结果，支持快速播放
- **热门搜索**：展示热门搜索关键词

### 13.1.2 用户场景
- 用户想要快速找到特定歌曲
- 用户想要发现新的音乐内容
- 用户想要查看搜索历史

## 13.2 个人中心模块 ("我的"页面)
### 13.2.1 核心功能
- **用户信息**：头像、昵称、个人简介
- **听汀统计**：收藏数量、播放时长、听歌偏好
- **播放历史**：最近播放的歌曲记录
- **个人设置**：快速访问设置页面
- **数据分析**：音乐品味分析和统计图表

### 13.2.2 用户场景
- 用户想要查看个人音乐数据
- 用户想要管理个人信息
- 用户想要快速访问收藏内容

## 13.3 音乐库管理模块
### 13.3.1 核心功能
- **分类浏览**：按歌曲、专辑、艺术家、播放列表分类
- **库统计**：音乐库容量、文件数量、格式分布
- **批量操作**：支持批量删除、移动、标签编辑
- **筛选排序**：多维度筛选和排序功能
- **库维护**：重复文件检测、损坏文件修复

### 13.3.2 用户场景
- 用户想要浏览所有音乐内容
- 用户想要管理音乐库
- 用户想要查看听汀统计

## 13.4 专辑详情模块
### 13.4.1 核心功能
- **专辑信息**：封面、标题、艺术家、发行年份
- **歌曲列表**：专辑内所有歌曲，支持单独播放
- **专辑播放**：一键播放整张专辑
- **收藏功能**：收藏/取消收藏专辑
- **相关推荐**：推荐相似专辑或同艺术家作品

### 13.4.2 用户场景
- 用户想要查看专辑详细信息
- 用户想要播放整张专辑
- 用户想要收藏喜欢的专辑

## 13.5 艺术家详情模块
### 13.5.1 核心功能
- **艺术家信息**：头像、简介、风格标签
- **热门歌曲**：艺术家最受欢迎的歌曲
- **专辑作品**：艺术家的所有专辑
- **关注功能**：关注/取消关注艺术家
- **相似推荐**：推荐风格相似的艺术家

### 13.5.2 用户场景
- 用户想要了解艺术家信息
- 用户想要听艺术家的热门歌曲
- 用户想要关注喜欢的艺术家

## 13.6 歌曲详情模块
### 13.6.1 核心功能
- **歌曲信息**：标题、艺术家、专辑、时长、格式
- **歌词展示**：完整歌词显示，支持滚动
- **音频信息**：比特率、采样率、文件大小
- **播放统计**：播放次数、最后播放时间
- **操作功能**：收藏、列表、分享

### 13.6.2 用户场景
- 用户想要查看歌曲详细信息
- 用户想要阅读歌词
- 用户想要对歌曲进行评价

## 13.7 播放列表管理模块
### 13.7.1 核心功能
- **创建列表**：自定义播放列表名称和封面
- **编辑功能**：添加/删除歌曲、调整顺序
- **智能列表**：基于心情、风格自动生成
- **分享功能**：导出播放列表为文件
- **列表统计**：歌曲数量、总时长、创建时间

### 13.7.2 用户场景
- 用户想要创建个人播放列表
- 用户想要管理播放列表
- 用户想要分享播放列表

## 13.8 我的锚点模块 (收藏管理)
### 13.8.1 核心功能
- **收藏展示**：所有收藏歌曲的统一展示
- **分类管理**：按时间、风格、心情分类
- **收藏统计**：收藏趋势、偏好分析
- **批量操作**：批量取消收藏、导出
- **锚点故事**：记录收藏歌曲的时间和心情

### 13.8.2 用户场景
- 用户想要查看所有收藏的歌曲
- 用户想要管理收藏内容
- 用户想要分析收藏偏好

## 13.9 音乐空间模块 (个性化展示)
### 13.9.1 核心功能
- **个人主页**：展示个人音乐品味和偏好
- **听歌报告**：周/月/年度听歌统计报告
- **音乐足迹**：记录音乐发现和收藏历程
- **品味分析**：基于听歌数据的音乐品味分析
- **分享功能**：分享个人音乐空间到社交平台

### 13.9.2 用户场景
- 用户想要展示个人音乐品味
- 用户想要查看听汀统计
- 用户想要分享音乐体验

## 13.10 均衡器设置模块
### 13.10.1 核心功能
- **图形均衡器**：10频段图形均衡器调节
- **预设模式**：摇滚、流行、古典、爵士等预设
- **自定义保存**：保存个人调音设置
- **实时预览**：调节时实时听到效果变化
- **音效增强**：低音增强、3D环绕、音场扩展

### 13.10.2 用户场景
- 用户想要调节音频效果
- 用户想要使用预设音效
- 用户想要保存个人音效设置

# 14. 系统功能模块 (v3.0) - 新增

## 14.1 桌面Widget模块
### 14.1.1 核心功能
- **迷你播放器**：显示当前播放歌曲信息
- **播放控制**：播放/暂停、上一首/下一首按钮
- **进度显示**：实时显示播放进度
- **快速启动**：点击Widget快速打开主应用
- **多尺寸支持**：小、中、大三种尺寸规格

### 14.1.2 用户场景
- 用户想要在桌面快速控制音乐播放
- 用户想要查看当前播放状态
- 用户想要快速访问音乐应用

## 14.2 用户账户模块
### 14.2.1 注册功能 (register.html)
- **注册方式**：邮箱注册、手机号注册
- **信息收集**：昵称、密码、头像选择
- **验证机制**：邮箱/短信验证码
- **协议确认**：用户协议和隐私政策确认
- **快速注册**：第三方账号快速注册

### 14.2.2 登录功能 (login.html)
- **登录方式**：邮箱/手机号 + 密码
- **记住密码**：本地安全存储登录状态
- **忘记密码**：密码重置功能
- **第三方登录**：微信、QQ、Apple ID登录
- **安全验证**：异常登录验证

### 14.2.3 用户场景
- 新用户想要创建账户
- 老用户想要登录账户
- 用户忘记密码需要重置

## 14.3 数据管理模块
### 14.3.1 备份恢复功能 (backup.html)
- **本地备份**：音乐库、播放列表、设置备份
- **云端同步**：支持iCloud、Google Drive同步
- **自动备份**：定时自动备份用户数据
- **数据恢复**：从备份文件恢复数据
- **导入导出**：支持数据文件导入导出

### 14.3.2 用户场景
- 用户想要备份重要数据
- 用户更换设备需要数据迁移
- 用户误删数据需要恢复

## 14.4 隐私安全模块
### 14.4.1 隐私设置 (privacy-settings.html)
- **数据收集控制**：选择性开启数据收集
- **个人信息管理**：查看和编辑个人信息
- **第三方共享**：控制数据第三方共享
- **数据删除**：彻底删除个人数据选项
- **隐私级别**：高、中、低三级隐私保护

### 14.4.2 用户场景
- 用户想要控制个人隐私
- 用户想要管理数据使用权限
- 用户想要了解数据收集情况

## 14.5 法律文档模块
### 14.5.1 用户协议 (user-agreement.html)
- **服务条款**：详细的服务使用条款
- **用户权利**：用户享有的权利说明
- **用户义务**：用户应承担的义务
- **协议变更**：协议更新通知机制
- **争议解决**：纠纷处理方式

### 14.5.2 隐私政策 (privacy-policy.html)
- **数据收集说明**：收集哪些数据及原因
- **数据使用方式**：如何使用用户数据
- **数据保护措施**：数据安全保护方法
- **用户权利说明**：用户对数据的控制权
- **联系方式**：隐私问题联系方式

### 14.5.3 用户场景
- 用户想要了解服务条款
- 用户想要了解隐私保护政策
- 用户需要查看法律条款

## 14.6 系统维护模块
### 14.6.1 检查更新 (update.html)
- **版本检查**：自动检查应用更新
- **更新日志**：详细的版本更新内容
- **更新选择**：自动更新或手动更新
- **更新进度**：实时显示更新进度
- **版本回退**：支持回退到上一版本

### 14.6.2 用户场景
- 用户想要获取最新功能
- 用户想要查看更新内容
- 用户想要控制更新方式

## 14.7 用户支持模块
### 14.7.1 联系我们 (contact.html)
- **多种联系方式**：邮箱、电话、在线客服
- **问题分类**：技术问题、功能建议、商务合作
- **反馈表单**：结构化问题反馈表单
- **常见问题**：FAQ快速解答
- **响应承诺**：客服响应时间说明

### 14.7.2 帮助与反馈 (help.html)
- **使用教程**：详细的功能使用指南
- **常见问题FAQ**：用户常遇问题解答
- **问题反馈**：Bug报告和功能建议
- **意见建议**：产品改进建议收集
- **技术支持**：技术问题解决方案

### 14.7.3 用户场景
- 用户遇到使用问题需要帮助
- 用户想要反馈Bug或建议
- 用户想要联系开发团队

## 14.8 产品介绍模块
### 14.8.1 功能介绍 (features.html)
- **核心功能展示**：主要功能特性介绍
- **特色功能亮点**：独特功能优势说明
- **使用场景演示**：实际使用场景展示
- **功能对比**：与竞品功能对比
- **更新功能**：最新功能特性介绍

### 14.8.2 引导页 (onboarding.html)
- **欢迎界面**：品牌介绍和产品理念
- **功能导览**：核心功能快速介绍
- **使用教程**：基础操作指导
- **个性化设置**：初始偏好设置
- **快速开始**：引导用户开始使用

### 14.8.3 用户场景
- 新用户想要了解产品功能
- 用户想要学习如何使用
- 用户想要了解产品特色

---

**PRD版本**：3.0
**创建日期**：2025年7月14日
**更新日期**：2025年7月19日
**产品经理**：John (v2.0/v3.0扩展功能) / Sarah (v1.0核心功能)
**状态**：已确认MVP范围和全功能需求，准备进入设计阶段