/**
 * 「听汀」交互动效系统 v1.0
 * 使用Alpine.js实现收藏动画、主题切换动画等微交互
 * 包含水波纹效果、锚点动画、主题切换等
 */

// 动画工具类
class AnimationUtils {
  /**
   * 创建水波纹效果
   * @param {HTMLElement} element - 目标元素
   * @param {Event} event - 点击事件
   * @param {string} color - 水波纹颜色
   */
  static createRipple(element, event, color = 'var(--color-primary)') {
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;
    
    const ripple = document.createElement('div');
    ripple.style.cssText = `
      position: absolute;
      border-radius: 50%;
      background: ${color};
      transform: scale(0);
      animation: ripple-animation 0.6s linear;
      left: ${x}px;
      top: ${y}px;
      width: ${size}px;
      height: ${size}px;
      opacity: 0.3;
      pointer-events: none;
    `;
    
    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);
    
    setTimeout(() => {
      ripple.remove();
    }, 600);
  }
  
  /**
   * 锚点下落动画
   * @param {HTMLElement} element - 锚点图标元素
   */
  static anchorDropAnimation(element) {
    const anchor = element.querySelector('iconify-icon') || element;
    
    // 创建动画序列
    anchor.style.animation = 'anchor-drop 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
    
    // 创建水波纹效果
    const rippleContainer = document.createElement('div');
    rippleContainer.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 40px;
      height: 40px;
      pointer-events: none;
    `;
    
    for (let i = 0; i < 3; i++) {
      const ripple = document.createElement('div');
      ripple.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 2px solid var(--color-accent);
        border-radius: 50%;
        opacity: 0;
        animation: anchor-ripple 1.2s ease-out ${i * 0.2}s;
      `;
      rippleContainer.appendChild(ripple);
    }
    
    element.style.position = 'relative';
    element.appendChild(rippleContainer);
    
    // 清理动画
    setTimeout(() => {
      anchor.style.animation = '';
      rippleContainer.remove();
    }, 1200);
  }
  
  /**
   * 主题切换动画
   * @param {string} newTheme - 新主题名称
   */
  static themeTransitionAnimation(newTheme) {
    const body = document.body;
    const overlay = document.createElement('div');
    
    overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle at center, 
        var(--color-primary) 0%, 
        transparent 70%);
      opacity: 0;
      pointer-events: none;
      z-index: 9999;
      animation: theme-transition-overlay 1200ms ease-out;
    `;
    
    body.appendChild(overlay);
    
    setTimeout(() => {
      overlay.remove();
    }, 1200);
  }
  
  /**
   * 卡片悬停动画
   * @param {HTMLElement} element - 卡片元素
   * @param {boolean} isHover - 是否悬停
   */
  static cardHoverAnimation(element, isHover) {
    if (isHover) {
      element.style.transform = 'translateY(-4px) scale(1.02)';
      element.style.boxShadow = 'var(--shadow-xl)';
    } else {
      element.style.transform = 'translateY(0) scale(1)';
      element.style.boxShadow = 'var(--shadow-md)';
    }
  }
  
  /**
   * 列表项滑入动画
   * @param {HTMLElement} element - 列表项元素
   * @param {number} delay - 延迟时间
   */
  static listItemSlideIn(element, delay = 0) {
    element.style.cssText += `
      opacity: 0;
      transform: translateX(-20px);
      animation: slide-in-left 0.4s ease-out ${delay}ms forwards;
    `;
  }
  
  /**
   * 播放按钮脉冲动画
   * @param {HTMLElement} element - 播放按钮元素
   * @param {boolean} isPlaying - 是否正在播放
   */
  static playButtonPulse(element, isPlaying) {
    if (isPlaying) {
      element.style.animation = 'pulse-playing 2s ease-in-out infinite';
    } else {
      element.style.animation = '';
    }
  }
  
  /**
   * 进度条动画更新
   * @param {HTMLElement} element - 进度条元素
   * @param {number} progress - 进度百分比
   */
  static updateProgressBar(element, progress) {
    const fill = element.querySelector('.progress-fill');
    if (fill) {
      fill.style.width = `${progress}%`;
      
      // 添加水波纹效果
      if (progress > 0) {
        fill.style.background = `
          linear-gradient(90deg, 
            var(--color-primary) 0%, 
            var(--color-accent) ${progress}%, 
            var(--color-primary) 100%)
        `;
      }
    }
  }
}

// Alpine.js 动画指令
document.addEventListener('alpine:init', () => {
  Alpine.directive('ripple', (el, { expression }, { evaluate }) => {
    el.addEventListener('click', (e) => {
      const color = evaluate(expression) || 'var(--color-primary)';
      AnimationUtils.createRipple(el, e, color);
    });
  });
  
  Alpine.directive('anchor-drop', (el) => {
    el.addEventListener('click', () => {
      AnimationUtils.anchorDropAnimation(el);
    });
  });
  
  Alpine.directive('card-hover', (el) => {
    el.addEventListener('mouseenter', () => {
      AnimationUtils.cardHoverAnimation(el, true);
    });
    
    el.addEventListener('mouseleave', () => {
      AnimationUtils.cardHoverAnimation(el, false);
    });
  });
  
  Alpine.directive('slide-in', (el, { expression }, { evaluate }) => {
    const delay = evaluate(expression) || 0;
    AnimationUtils.listItemSlideIn(el, delay);
  });
});

// 全局动画样式注入
const animationStyles = `
  @keyframes ripple-animation {
    to {
      transform: scale(4);
      opacity: 0;
    }
  }
  
  @keyframes anchor-drop {
    0% {
      transform: translateY(-20px) rotate(0deg);
      opacity: 0;
    }
    50% {
      transform: translateY(5px) rotate(180deg);
      opacity: 1;
    }
    100% {
      transform: translateY(0) rotate(360deg);
      opacity: 1;
    }
  }
  
  @keyframes anchor-ripple {
    0% {
      transform: scale(0);
      opacity: 1;
    }
    100% {
      transform: scale(2);
      opacity: 0;
    }
  }
  
  @keyframes theme-transition-overlay {
    0% {
      opacity: 0;
      transform: scale(0);
    }
    50% {
      opacity: 0.1;
      transform: scale(1);
    }
    100% {
      opacity: 0;
      transform: scale(1.2);
    }
  }
  
  @keyframes slide-in-left {
    0% {
      opacity: 0;
      transform: translateX(-20px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes pulse-playing {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(74, 144, 226, 0.4);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 0 10px rgba(74, 144, 226, 0);
    }
  }
  
  @keyframes float-gentle {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  @keyframes glow-pulse {
    0%, 100% {
      box-shadow: 0 0 5px var(--color-accent);
    }
    50% {
      box-shadow: 0 0 20px var(--color-accent), 0 0 30px var(--color-accent);
    }
  }
  
  /* 交互状态类 */
  .animate-ripple {
    position: relative;
    overflow: hidden;
  }
  
  .animate-anchor-drop {
    transition: all 0.3s ease;
  }
  
  .animate-card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .animate-slide-in {
    animation: slide-in-left 0.4s ease-out forwards;
  }
  
  .animate-pulse-playing {
    animation: pulse-playing 2s ease-in-out infinite;
  }
  
  .animate-float {
    animation: float-gentle 3s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow-pulse 2s ease-in-out infinite;
  }
  
  /* 悬停效果增强 */
  .hover-lift:hover {
    transform: translateY(-2px);
    transition: transform 0.2s ease;
  }
  
  .hover-scale:hover {
    transform: scale(1.05);
    transition: transform 0.2s ease;
  }
  
  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(74, 144, 226, 0.3);
    transition: box-shadow 0.3s ease;
  }
  
  /* 焦点状态 */
  .focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.3);
  }
  
  /* 加载状态 */
  .loading-spinner {
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  /* 减少动画偏好设置 */
  @media (prefers-reduced-motion: reduce) {
    .animate-ripple,
    .animate-anchor-drop,
    .animate-card-hover,
    .animate-slide-in,
    .animate-pulse-playing,
    .animate-float,
    .animate-glow,
    .hover-lift,
    .hover-scale,
    .hover-glow {
      animation: none !important;
      transition: none !important;
    }
  }
`;

// 注入动画样式
const styleSheet = document.createElement('style');
styleSheet.textContent = animationStyles;
document.head.appendChild(styleSheet);

// 页面加载动画
document.addEventListener('DOMContentLoaded', () => {
  // 为列表项添加渐入动画
  const listItems = document.querySelectorAll('.song-item, .album-card, .settings-item');
  listItems.forEach((item, index) => {
    AnimationUtils.listItemSlideIn(item, index * 50);
  });
  
  // 为卡片添加悬停效果
  const cards = document.querySelectorAll('.card, .current-playing-card, .album-card');
  cards.forEach(card => {
    card.classList.add('animate-card-hover');
  });
  
  console.log('🎭 动画系统已加载');
});

// 主题切换动画监听
document.addEventListener('themechange', (e) => {
  AnimationUtils.themeTransitionAnimation(e.detail.theme);
});

// 导出动画工具类
window.AnimationUtils = AnimationUtils;

// 为 Alpine.js 提供动画功能
if (window.Alpine) {
  window.Alpine.data('animations', () => ({
    rippleClick(event, color) {
      AnimationUtils.createRipple(this.$el, event, color);
    },
    
    anchorDrop() {
      AnimationUtils.anchorDropAnimation(this.$el);
    },
    
    cardHover(isHover) {
      AnimationUtils.cardHoverAnimation(this.$el, isHover);
    },
    
    playButtonPulse(isPlaying) {
      AnimationUtils.playButtonPulse(this.$el, isPlaying);
    },
    
    updateProgress(progress) {
      AnimationUtils.updateProgressBar(this.$el, progress);
    }
  }));
}

console.log('🌊 听汀动画系统加载完成');
