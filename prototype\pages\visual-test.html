<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视觉一致性测试 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
</head>
<body class="font-primary">
    <!-- 主题管理器 -->
    <script src="../assets/js/theme.js"></script>
    
    <!-- 视觉测试页面 -->
    <div class="main-container water-bg" x-data="visualTest()">
        <!-- 顶部导航栏测试 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="header-brand">
                    <iconify-icon icon="material-symbols:anchor" class="header-brand-icon"></iconify-icon>
                    <h1 class="header-brand-text">视觉测试</h1>
                </div>
                
                <div class="header-actions">
                    <button class="header-button" aria-label="搜索">
                        <iconify-icon icon="material-symbols:search" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-theme-toggle" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                        <span class="theme-text text-sm font-medium text-primary">晨汀</span>
                    </button>
                    
                    <button class="header-button" aria-label="设置">
                        <iconify-icon icon="material-symbols:settings" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 主要内容区域 -->
        <main class="px-6 py-8">
            <div class="max-w-4xl mx-auto space-y-8">
                <!-- 卡片组件测试 -->
                <section>
                    <h2 class="title-lg mb-4">卡片组件</h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <div class="card p-6">
                            <h3 class="title-md mb-2">标准卡片</h3>
                            <p class="text-secondary">这是一个标准的卡片组件，展示基础样式。</p>
                        </div>
                        
                        <div class="current-playing-card p-6">
                            <h3 class="title-md mb-2">播放卡片</h3>
                            <p class="text-secondary">这是当前播放的卡片样式。</p>
                        </div>
                        
                        <div class="mini-player-card">
                            <div class="mini-player-cover">
                                <iconify-icon icon="material-symbols:music-note"></iconify-icon>
                            </div>
                            <div class="mini-player-info">
                                <div class="mini-player-title">迷你播放器</div>
                                <div class="mini-player-artist">测试艺术家</div>
                            </div>
                        </div>
                    </div>
                </section>
                
                <!-- 按钮组件测试 -->
                <section>
                    <h2 class="title-lg mb-4">按钮组件</h2>
                    <div class="flex flex-wrap gap-4">
                        <button class="header-button">
                            <iconify-icon icon="material-symbols:home" class="text-lg"></iconify-icon>
                        </button>
                        
                        <button class="control-button">
                            <iconify-icon icon="material-symbols:play-arrow" class="text-lg"></iconify-icon>
                        </button>
                        
                        <button class="control-button primary">
                            <iconify-icon icon="material-symbols:play-arrow" class="text-lg"></iconify-icon>
                        </button>
                        
                        <button class="control-button active">
                            <iconify-icon icon="material-symbols:favorite" class="text-lg"></iconify-icon>
                        </button>
                        
                        <button class="player-control-button">
                            <iconify-icon icon="material-symbols:skip-previous" class="text-xl"></iconify-icon>
                        </button>
                        
                        <button class="player-control-button play">
                            <iconify-icon icon="material-symbols:play-arrow" class="text-2xl"></iconify-icon>
                        </button>
                    </div>
                </section>
                
                <!-- 进度条组件测试 -->
                <section>
                    <h2 class="title-lg mb-4">进度条组件</h2>
                    <div class="space-y-4">
                        <div class="progress-container">
                            <div class="progress-fill" style="width: 60%">
                                <div class="progress-thumb"></div>
                            </div>
                        </div>
                        
                        <div class="volume-slider">
                            <div class="volume-fill" style="width: 75%"></div>
                        </div>
                    </div>
                </section>
                
                <!-- 色彩系统测试 -->
                <section>
                    <h2 class="title-lg mb-4">色彩系统</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <div class="p-4 rounded-lg" style="background: var(--color-primary); color: white;">
                            <div class="font-semibold">主色</div>
                            <div class="text-sm opacity-90">Primary</div>
                        </div>
                        
                        <div class="p-4 rounded-lg" style="background: var(--color-accent); color: white;">
                            <div class="font-semibold">强调色</div>
                            <div class="text-sm opacity-90">Accent</div>
                        </div>
                        
                        <div class="p-4 rounded-lg" style="background: var(--bg-primary); color: var(--text-primary); border: 1px solid var(--border-color);">
                            <div class="font-semibold">背景色</div>
                            <div class="text-sm opacity-75">Background</div>
                        </div>
                        
                        <div class="p-4 rounded-lg" style="background: var(--card-bg); color: var(--text-primary); border: 1px solid var(--border-color);">
                            <div class="font-semibold">卡片色</div>
                            <div class="text-sm opacity-75">Card</div>
                        </div>
                    </div>
                </section>
                
                <!-- 字体系统测试 -->
                <section>
                    <h2 class="title-lg mb-4">字体系统</h2>
                    <div class="space-y-4">
                        <div class="title-xl">超大标题 - Title XL</div>
                        <div class="title-lg">大标题 - Title LG</div>
                        <div class="title-md">中标题 - Title MD</div>
                        <div class="text-primary">主要文本 - Primary Text</div>
                        <div class="text-secondary">次要文本 - Secondary Text</div>
                        <div class="text-accent">强调文本 - Accent Text</div>
                    </div>
                </section>
            </div>
        </main>
        
        <!-- 底部控制栏测试 -->
        <div class="bottom-control-container glass-morphism">
            <div class="mini-player-card">
                <div class="mini-player-cover">
                    <iconify-icon icon="material-symbols:music-note"></iconify-icon>
                </div>
                
                <div class="mini-player-info">
                    <div class="mini-player-title">测试歌曲标题</div>
                    <div class="mini-player-artist">测试艺术家</div>
                </div>
                
                <button class="header-button">
                    <iconify-icon icon="material-symbols:anchor-outline" class="text-lg"></iconify-icon>
                </button>
            </div>
            
            <div class="control-buttons-row">
                <div class="control-button-group">
                    <button class="control-button">
                        <iconify-icon icon="material-symbols:skip-previous" class="text-xl"></iconify-icon>
                    </button>
                    
                    <button class="control-button primary">
                        <iconify-icon icon="material-symbols:play-arrow" class="text-xl"></iconify-icon>
                    </button>
                    
                    <button class="control-button">
                        <iconify-icon icon="material-symbols:skip-next" class="text-xl"></iconify-icon>
                    </button>
                </div>
                
                <div class="control-button-group">
                    <button class="control-button">
                        <iconify-icon icon="material-symbols:shuffle" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button class="control-button">
                        <iconify-icon icon="material-symbols:repeat" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button class="control-button">
                        <iconify-icon icon="material-symbols:fullscreen" class="text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function visualTest() {
            return {
                init() {
                    console.log('🎨 视觉一致性测试页面已加载');
                }
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 视觉测试页面');
        });
    </script>
</body>
</html>
