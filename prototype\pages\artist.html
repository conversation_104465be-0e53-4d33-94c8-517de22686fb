<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>艺术家 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .artist-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .artist-avatar {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 4rem;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(74, 144, 226, 0.3);
            margin: 0 auto;
        }
        
        .artist-avatar::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: pulse 3s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.1); opacity: 1; }
        }
        
        .artist-info-card {
            background: var(--card-bg);
            border-radius: 24px;
            padding: 2.5rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            text-align: center;
            margin-top: 2rem;
        }
        
        .work-item {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            cursor: pointer;
            backdrop-filter: blur(10px);
        }
        
        .work-item:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        
        .work-cover {
            width: 80px;
            height: 80px;
            border-radius: 12px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            flex-shrink: 0;
            margin-right: 1rem;
        }
        
        .follow-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 2rem;
            border-radius: 24px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .follow-button.following {
            background: var(--color-accent);
            color: white;
        }
        
        .follow-button.not-following {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 2px solid var(--color-primary);
        }
        
        .follow-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
        }
        
        .style-tag {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 20px;
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin: 0.25rem;
            position: relative;
            overflow: hidden;
        }
        
        .style-tag::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="artist-container water-bg" x-data="artistApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">艺术家</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="shareArtist()" class="header-button" aria-label="分享艺术家">
                        <iconify-icon icon="material-symbols:share" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 艺术家信息区域 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 艺术家头像 -->
                <div class="artist-avatar mb-6">
                    <iconify-icon icon="material-symbols:person" x-show="!artistInfo.avatar"></iconify-icon>
                    <img :src="artistInfo.avatar" :alt="artistInfo.name" class="w-full h-full object-cover rounded-full" x-show="artistInfo.avatar">
                </div>
                
                <!-- 艺术家信息卡片 -->
                <div class="artist-info-card">
                    <h2 class="text-3xl font-bold text-primary mb-3" x-text="artistInfo.name"></h2>
                    <p class="text-lg text-secondary mb-6" x-text="artistInfo.description"></p>
                    
                    <!-- 统计信息 -->
                    <div class="grid grid-cols-3 gap-6 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="artistInfo.stats.albums"></div>
                            <div class="text-sm text-secondary">专辑</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="artistInfo.stats.songs"></div>
                            <div class="text-sm text-secondary">歌曲</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-primary" x-text="artistInfo.stats.followers"></div>
                            <div class="text-sm text-secondary">关注者</div>
                        </div>
                    </div>
                    
                    <!-- 音乐风格标签 -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-primary mb-3">音乐风格</h3>
                        <div class="flex flex-wrap justify-center">
                            <template x-for="style in artistInfo.styles" :key="style">
                                <span class="style-tag" x-text="style"></span>
                            </template>
                        </div>
                    </div>
                    
                    <!-- 关注按钮 -->
                    <button @click="toggleFollow()" 
                            :class="artistInfo.isFollowing ? 'following' : 'not-following'" 
                            class="follow-button">
                        <iconify-icon :icon="artistInfo.isFollowing ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'" class="text-lg"></iconify-icon>
                        <span x-text="artistInfo.isFollowing ? '已关注' : '关注'"></span>
                    </button>
                </div>
                
                <!-- 热门作品 -->
                <div class="mt-8">
                    <h3 class="text-xl font-semibold text-primary mb-6">热门作品</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <template x-for="work in popularWorks" :key="work.id">
                            <div @click="playWork(work)" class="work-item">
                                <div class="flex items-center">
                                    <div class="work-cover">
                                        <iconify-icon :icon="work.type === 'album' ? 'material-symbols:album' : 'material-symbols:music-note'" x-show="!work.cover"></iconify-icon>
                                        <img :src="work.cover" :alt="work.title" class="w-full h-full object-cover rounded-xl" x-show="work.cover">
                                    </div>
                                    
                                    <div class="flex-1 min-w-0">
                                        <h4 class="font-semibold text-primary truncate mb-1" x-text="work.title"></h4>
                                        <p class="text-sm text-secondary truncate mb-2" x-text="work.year + ' • ' + work.type"></p>
                                        <div class="flex items-center text-xs text-secondary">
                                            <iconify-icon icon="material-symbols:play-arrow" class="mr-1"></iconify-icon>
                                            <span x-text="formatPlays(work.plays)"></span>
                                        </div>
                                    </div>
                                    
                                    <button @click.stop="toggleWorkFavorite(work)" class="p-2 rounded-lg hover:bg-bg-secondary transition-colors">
                                        <iconify-icon :icon="work.isFavorite ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'" 
                                                      :class="work.isFavorite ? 'text-accent' : 'text-secondary'"
                                                      class="text-lg"></iconify-icon>
                                    </button>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function artistApp() {
            return {
                artistInfo: {
                    name: '古风音乐',
                    description: '专注于传统文化与现代音乐的融合，致力于传承和发扬中华音乐文化',
                    avatar: null,
                    isFollowing: false,
                    stats: {
                        albums: 12,
                        songs: 156,
                        followers: 2847
                    },
                    styles: ['古风', '民族', '新中式', '器乐', '诗词歌赋']
                },
                
                popularWorks: [
                    {
                        id: 1,
                        title: '诗词歌赋',
                        type: '专辑',
                        year: 2024,
                        plays: 1250000,
                        cover: null,
                        isFavorite: true
                    },
                    {
                        id: 2,
                        title: '水调歌头',
                        type: '单曲',
                        year: 2024,
                        plays: 890000,
                        cover: null,
                        isFavorite: false
                    },
                    {
                        id: 3,
                        title: '古韵新声',
                        type: '专辑',
                        year: 2023,
                        plays: 750000,
                        cover: null,
                        isFavorite: true
                    },
                    {
                        id: 4,
                        title: '静夜思',
                        type: '单曲',
                        year: 2023,
                        plays: 650000,
                        cover: null,
                        isFavorite: false
                    }
                ],
                
                toggleFollow() {
                    this.artistInfo.isFollowing = !this.artistInfo.isFollowing;
                    if (this.artistInfo.isFollowing) {
                        this.artistInfo.stats.followers++;
                        console.log('⚓ 已关注艺术家:', this.artistInfo.name);
                    } else {
                        this.artistInfo.stats.followers--;
                        console.log('○ 取消关注艺术家:', this.artistInfo.name);
                    }
                },
                
                playWork(work) {
                    console.log('🎵 播放作品:', work.title);
                    if (work.type === '专辑') {
                        window.location.href = './album.html';
                    } else {
                        window.location.href = './player.html';
                    }
                },
                
                toggleWorkFavorite(work) {
                    work.isFavorite = !work.isFavorite;
                    console.log(work.isFavorite ? '⚓ 作品已收藏' : '○ 取消收藏作品', work.title);
                },
                
                formatPlays(plays) {
                    if (plays >= 1000000) {
                        return Math.floor(plays / 100000) / 10 + 'M';
                    } else if (plays >= 1000) {
                        return Math.floor(plays / 100) / 10 + 'K';
                    }
                    return plays.toString();
                },
                
                shareArtist() {
                    console.log('📤 分享艺术家:', this.artistInfo.name);
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('🎨 艺术家页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 艺术家页面');
        });
    </script>
</body>
</html>
