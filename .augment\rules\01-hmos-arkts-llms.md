---
type: "manual"
---

# HarmonyOS开发 - LLMs最佳实践规范

## 概述

本文档为大语言模型(LLMs)提供HarmonyOS应用开发的标准化指导，基于HarmonyOS代码工坊项目的实际架构和最佳实践。确保AI协同开发的一致性和代码质量。

## 核心架构原则

### 1. 严格分层架构 (CRITICAL)

**必须遵循的三层架构**:
```
Products层 (HAP包) → Features层 (HAR包) → Common层 (HAR包)
```

**依赖规则**:
- ✅ Products可以依赖Features和Common
- ✅ Features可以依赖Common和其他Features
- ❌ 绝对禁止反向依赖
- ❌ Common层不能依赖任何业务层

**实际应用**:
```typescript
// ✅ 正确: Products层引用Features
import { ComponentLibraryView } from '@ohos/componentlibrary';

// ✅ 正确: Features层引用Common
import { Logger, CommonConstants } from '@ohos/common';

// ❌ 错误: Common层引用Features
import { SampleCard } from '@ohos/devpractices'; // 禁止!
```

### 2. 模块包类型规范

**HAP包 (应用入口)**:
- 位置: `products/` 目录
- 用途: 设备特定的应用入口
- 配置: `"type": "entry"` in module.json5

**HAR包 (静态库)**:
- 位置: `features/` 和 `common/` 目录
- 用途: 可复用的业务模块和公共能力
- 配置: `"type": "har"` in module.json5

**设备类型配置**:
```json5
// 手机/平板/PC共包
"deviceTypes": ["phone", "tablet", "2in1"]
```

## ArkTS编码规范

### 1. 组件定义标准

**组件装饰器使用**:
```typescript
// ✅ 页面级组件
@Entry
@Component
struct MainPage {
  // 实现
}

// ✅ 可复用组件
@Component
export struct SampleCard {
  // 实现
}

// ✅ 性能优化组件
@Component({ freezeWhenInactive: true })
export struct PracticesView {
  // 实现
}
```

**状态管理模式**:
```typescript
// ✅ 全局状态
@StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = 
  AppStorage.get('GlobalInfoModel')!;

// ✅ 父子组件通信
@ObjectLink sampleCard: SampleCardData;
@Prop @Require sampleIndex: number;

// ✅ 跨组件状态
@Consume currentIndex: number;
@Provide('currentIndex') currentIndex: number = 0;
```

### 2. 响应式设计模式

**断点适配**:
```typescript
// ✅ 使用BreakpointType进行响应式设计
new BreakpointType({
  sm: CommonConstants.SPACE_16,
  md: CommonConstants.SPACE_16,
  lg: CommonConstants.SPACE_24,
}).getValue(this.globalInfoModel.currentBreakpoint)
```

**设备适配**:
```typescript
// ✅ 根据设备类型调整UI
this.globalInfoModel.currentBreakpoint === BreakpointTypeEnum.XL ? 
  Color.Transparent : $r('sys.color.comp_background_tertiary')
```

### 3. 路由管理规范

**路由定义**:
```typescript
// ✅ 使用NavPathStack进行路由管理
export class PageContext implements IPageContext {
  private readonly pathStack: NavPathStack;
  
  public openPage(data: RouterParam, animated: boolean = true): void {
    this.pathStack.pushPath({
      name: data.routerName,
      param: data.param,
    }, animated);
  }
}
```

**路由配置**:
```json
// router_map.json
{
  "routerMap": [
    {
      "name": "ComponentDetailView",
      "pageSourceFile": "src/main/ets/view/ComponentDetailView.ets",
      "buildFunction": "ComponentDetailBuilder"
    }
  ]
}
```

## 文件组织规范

### 1. 目录结构标准

**Features模块结构**:
```
features/[module-name]/
├── src/main/ets/
│   ├── component/          # 模块特定组件
│   ├── view/              # 页面视图
│   ├── viewmodel/         # 状态管理和业务逻辑
│   ├── service/           # 数据服务
│   ├── constant/          # 常量定义
│   └── model/             # 数据模型
├── src/main/resources/    # 模块资源
├── Index.ets             # 模块导出入口
├── module.json5          # 模块配置
├── oh-package.json5      # 依赖配置
└── build-profile.json5   # 构建配置
```

**Common层结构**:
```
common/
├── src/main/ets/
│   ├── component/         # 公共UI组件
│   ├── routermanager/     # 路由管理
│   ├── storagemanager/    # 存储管理
│   ├── util/              # 工具类
│   ├── viewmodel/         # 基础ViewModel
│   └── constant/          # 全局常量
└── Index.ets             # 公共能力导出
```

### 2. 命名约定

**文件命名**:
- 组件文件: `PascalCase.ets` (如: `SampleCard.ets`)
- 页面文件: `PascalCase.ets` (如: `MainPage.ets`)
- 工具类: `PascalCase.ets` (如: `Logger.ets`)
- 常量文件: `PascalCase.ets` (如: `CommonConstants.ets`)

**导出规范**:
```typescript
// ✅ Index.ets中统一导出
export { SampleCard } from './src/main/ets/component/SampleCard';
export { PracticesView } from './src/main/ets/view/PracticesView';
export { SampleDetailPageVM } from './src/main/ets/viewmodel/SampleDetailPageVM';
```

## 构建和配置规范

### 1. 包依赖管理

**oh-package.json5配置**:
```json5
{
  "name": "module-name",
  "version": "1.0.0",
  "main": "Index.ets",
  "license": "Apache-2.0",
  "dependencies": {
    "@ohos/common": "file:../../common",
    "@ohos/commonbusiness": "file:../commonbusiness"
  }
}
```

### 2. 构建配置

**module.json5标准**:
```json5
{
  "module": {
    "name": "module-name",
    "type": "har", // 或 "entry"
    "deviceTypes": ["default", "tablet", "2in1"],
    "routerMap": "$profile:router_map" // 如果有路由
  }
}
```

**混淆配置**:
```json5
// build-profile.json5
{
  "buildOptionSet": [
    {
      "name": "release",
      "arkOptions": {
        "obfuscation": {
          "ruleOptions": {
            "enable": true,
            "files": ["./obfuscation-rules.txt"]
          },
          "consumerFiles": ["./consumer-rules.txt"]
        }
      }
    }
  ]
}
```

## 性能优化指导

### 1. 组件性能

**懒加载组件**:
```typescript
// ✅ 使用freezeWhenInactive优化性能
@Component({ freezeWhenInactive: true })
export struct HeavyComponent {
  // 实现
}
```

**条件渲染**:
```typescript
// ✅ 合理使用条件渲染
if (this.showContent) {
  ContentComponent()
}
```

### 2. 内存管理

**Hvigor内存配置**:
```json5
// hvigor-config.json5
{
  "properties": {
    "hvigor.pool.cache.capacity": 0,
    "hvigor.pool.maxSize": 5,
    "ohos.arkCompile.maxSize": 3,
    "hvigor.enableMemoryCache": false
  }
}
```

## 数据管理规范

### 1. ViewModel层架构模式

**ViewModel文件结构**:
```
features/[module]/src/main/ets/viewmodel/
├── [Module]State.ets          # 状态定义
├── [Module]ViewModel.ets       # 业务逻辑处理
└── [Module]PageVM.ets         # 页面级ViewModel (可选)
```

**State文件模式 (xxState.ets)**:
```typescript
// ✅ 状态类定义标准
@Observed
export class SampleDetailState extends BaseState {
  public sampleDatas: SampleDetailData[] = [];
  public sampleCount: number = 0;
  public loadingStatus: LoadingStatus = LoadingStatus.LOADING;
  public currentIndex: number = 0;
  public installingStatus: boolean = false;

  constructor() {
    super();
  }
}

// ✅ 数据模型定义
@Observed
export class SampleDetailData {
  public id: number = 0;
  public isFavorite: boolean = false;
  public mediaType: number = 0;
  public mediaUrl: string = '';
  public sampleCard: SampleCardData = new SampleCardData();
}
```

**ViewModel文件模式 (xxVM.ets)**:
```typescript
// ✅ ViewModel基础结构
export class SampleDetailPageVM extends BaseVM<SampleDetailState> {
  private static instance: SampleDetailPageVM;
  private model: SampleDetailModel = SampleDetailModel.getInstance();

  private constructor() {
    super(new SampleDetailState());
  }

  public static getInstance(): SampleDetailPageVM {
    if (!SampleDetailPageVM.instance) {
      SampleDetailPageVM.instance = new SampleDetailPageVM();
    }
    return SampleDetailPageVM.instance;
  }

  // 事件处理
  public sendEvent(event: BaseVMEvent): void {
    switch (event.eventType) {
      case SampleDetailEventType.LOAD_SAMPLE:
        this.loadSample();
        break;
      case SampleDetailEventType.DOWNLOAD_SAMPLE:
        this.downloadSample(event.data);
        break;
    }
  }
}
```

### 2. Model层架构模式

**Model文件结构**:
```
features/[module]/src/main/ets/model/
├── [Entity]Model.ets          # 业务模型 (单例，处理业务逻辑)
├── [Entity]Data.ets           # 数据结构定义
└── [Entity]Param.ets          # 参数传递对象
```

**业务模型文件 (xxModel.ets)**:
```typescript
// ✅ 业务模型标准结构
export class SampleModel {
  private service: SampleService = new SampleService();
  private static instance: SampleModel;

  private constructor() {}

  public static getInstance(): SampleModel {
    if (!SampleModel.instance) {
      SampleModel.instance = new SampleModel();
    }
    return SampleModel.instance;
  }

  // 业务方法
  public getSamplePage(currentPage: number, pageSize: number): Promise<ResponseData<SampleData>> {
    return this.service.getSamplePageByPreference(currentPage, pageSize)
      .then((data: ResponseData<SampleData>) => {
        return data;
      }).catch((err: string) => {
        Logger.error(TAG, `getSamplePage failed: ${err}`);
        return this.service.getSamplePage(currentPage, pageSize);
      });
  }
}
```

**数据结构文件 (xxData.ets)**:
```typescript
// ✅ 数据实体定义
export class SampleCardData {
  public id: number = 0;
  public cardTitle: string = '';
  public cardSubTitle: string = '';
  public cardType: CardTypeEnum = CardTypeEnum.UNKNOWN;
  public cardImage: string = '';
  public version: string = '';
  public sampleContents: SampleContent[] = [];
}

// ✅ 复杂数据结构
@Observed
export class SampleCategory {
  public loadingModel: LoadingModel = new LoadingModel();
  public currentPage: number = 1;
  public id: number = 0;
  public categoryName: string = '';
  public categoryType: number = 0;
  public sampleCards: SampleCardData[] = [];
}
```

**参数对象文件 (xxParam.ets)**:
```typescript
// ✅ 路由参数定义
export interface SampleDetailParams {
  currentIndex: number;
  sampleCardId: number;
}

export interface ComponentDetailParams {
  componentName: string;
  componentId: number;
}

// ✅ 事件参数定义
export interface CalculateHeightParam {
  yOffset: number;
  offset: number;
  state: ScrollState;
}

export interface OffsetParam {
  yOffset: number;
  tabIndex: number;
  breakpointChange?: boolean;
}
```

### 3. Service层架构模式

**Service文件结构**:
```
features/[module]/src/main/ets/service/
└── [Module]Service.ets        # 数据服务层
```

**Service实现模式**:
```typescript
// ✅ Service层标准结构
export class SampleService {
  public constructor() {}

  // Mock数据获取
  public getSampleListByMock(): Promise<ResponseData<SampleData>> {
    return new Promise((resolve, reject) => {
      MockRequest.call<ResponseData<SampleData>>(SampleTrigger.SAMPLE_PAGE)
        .then((result: Object) => {
          resolve(result as ResponseData<SampleData>);
        })
        .catch((error: BusinessError) => {
          reject(error);
        });
    });
  }

  // 网络请求
  public getSamplePage(currentPage?: number, pageSize?: number): Promise<ResponseData<SampleData>> {
    Logger.info(TAG, `getSamplePage param: currentPage ${currentPage}, pageSize: ${pageSize}`);
    return this.getSampleListByMock();
  }

  // 数据持久化
  public setSamplePageToPreference(data: ResponseData<SampleData>): void {
    PreferenceManager.getInstance().setValue(SampleTrigger.SAMPLE_PAGE, data);
  }
}
```

### 4. 常量和枚举定义

**常量文件结构**:
```
features/[module]/src/main/ets/constant/
└── [Module]Constants.ets      # 模块常量定义

common/src/main/ets/constant/
├── CommonConstants.ets        # 通用常量
└── CommonEnums.ets           # 通用枚举
```

**常量定义模式**:
```typescript
// ✅ 常量类定义
export class CommonConstants {
  // 尺寸常量
  public static readonly NAVIGATION_HEIGHT: number = 56;
  public static readonly TAB_BAR_HEIGHT: number = 48;
  public static readonly SPACE_8: number = 8;
  public static readonly SPACE_16: number = 16;

  // 百分比常量
  public static readonly FULL_PERCENT: string = '100%';

  // 功能常量
  public static readonly DYNAMIC_INSTALL_EVENT: string = 'DynamicInstallEvent';
  public static readonly PROMISE_WAIT = (delay: number) => new Promise<void>((resolve) => setTimeout(resolve, delay));
}

// ✅ 枚举定义
export enum LoadingStatus {
  IDLE = 'idle',
  LOADING = 'loading',
  SUCCESS = 'success',
  FAILED = 'failed',
  NO_NETWORK = 'no_network',
}

export enum ColumnEnum {
  SM = 4,
  MD = 8,
  LG = 12,
}
```

### 5. 工具类架构模式

**工具类文件结构**:
```
common/src/main/ets/util/
├── Logger.ets                # 日志工具
├── WindowUtil.ets            # 窗口工具
├── ImageUtil.ets             # 图片工具
├── BreakpointSystem.ets      # 断点系统
└── [Specific]Util.ets        # 特定功能工具
```

**工具类实现模式**:
```typescript
// ✅ 单例工具类
class Logger {
  private domain: number;
  private prefix: string;
  private format: string = '%{public}s, %{public}s';

  public constructor(prefix: string) {
    this.prefix = prefix;
    this.domain = 0xFF00;
  }

  public debug(...args: Object[]): void {
    hilog.debug(this.domain, this.prefix, this.format, args);
  }

  public info(...args: Object[]): void {
    hilog.info(this.domain, this.prefix, this.format, args);
  }

  public error(...args: Object[]): void {
    hilog.error(this.domain, this.prefix, this.format, args);
  }
}

export default new Logger('[HMOSWorld]');

// ✅ 静态工具类
export class ImageUtil {
  public static getColorFromImgUrl(imgUrl: string, isDeepColor?: boolean): Promise<number[]> {
    return new Promise((resolve, reject) => {
      ImageUtil.getColorDataByPreference(imgUrl).then((data: number[]) => {
        resolve(data);
      }).catch(() => {
        ImageUtil.getColorByPath(imgUrl, isDeepColor).then((colorData: number[]) => {
          resolve(colorData);
        }).catch((err: BusinessError) => {
          Logger.error(TAG, `Failed to get color data: ${err.message}`);
          reject(err);
        });
      });
    });
  }
}
```

### 6. 数据持久化

**PreferenceManager使用**:
```typescript
// ✅ 统一的存储管理
PreferenceManager.getInstance().setValue(key, value);
PreferenceManager.getInstance().getValue(key, defaultValue);
```

## 组件层架构模式

### 1. 组件文件结构

**组件目录组织**:
```
features/[module]/src/main/ets/component/
├── [Feature]Component.ets     # 功能组件
├── [UI]Card.ets              # 卡片组件
└── [Specific]View.ets        # 特定视图组件
```

**组件实现模式**:
```typescript
// ✅ 功能组件标准结构
@Component
export struct SampleComponent {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  @ObjectLink singleSampleData: SampleDetailData;
  @Prop @Require sampleIndex: number;
  @Prop showIndicator: boolean = false;

  build() {
    Column() {
      // 组件内容
      if (this.singleSampleData.mediaType === MediaTypeEnum.VIDEO) {
        VideoComponent({
          videoUrl: this.singleSampleData.mediaUrl,
          showIndicator: this.showIndicator
        })
      } else {
        SampleCard({
          sampleCard: this.singleSampleData.sampleCard,
          sampleIndex: this.sampleIndex
        })
      }
    }
    .width('100%')
    .height('100%')
  }
}

// ✅ 卡片组件模式
@Component
export struct SampleCard {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  @ObjectLink sampleCard: SampleCardData;
  @Prop sampleIndex: number;
  viewModel: SampleDetailPageVM = SampleDetailPageVM.getInstance();

  build() {
    Column() {
      // 卡片内容
      Text(this.sampleCard.title)
        .fontSize($r('sys.float.Body_L'))
        .fontColor($r('sys.color.font_emphasize'))

      Row() {
        Button($r('app.string.read_code'))
          .onClick(() => {
            this.viewModel.sendEvent(new BindSheetEvent(this.sampleIndex, true));
          })

        Button($r('app.string.download_sample'))
          .onClick(() => {
            this.viewModel.sendEvent(new LoadSampleEvent());
          })
      }
    }
    .borderRadius($r('sys.float.corner_radius_level8'))
    .backgroundColor($r('sys.color.comp_background_primary'))
  }
}
```

### 2. 页面视图模式

**页面文件结构**:
```
features/[module]/src/main/ets/view/
└── [Module]View.ets           # 页面视图

products/[device]/src/main/ets/page/
└── [Page]Page.ets            # 页面入口
```

**页面实现模式**:
```typescript
// ✅ 页面级组件
@Entry
@Component
struct MainPage {
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  @State currentIndex: number = 0;
  private tabController: TabsController = new TabsController();

  build() {
    Tabs({ controller: this.tabController }) {
      TabContent() {
        ComponentLibraryView()
      }.tabBar(this.TabBuilder(TabBarType.HOME))

      TabContent() {
        PracticesView()
      }.tabBar(this.TabBuilder(TabBarType.SAMPLE))
    }
    .onChange((index: number) => {
      this.currentIndex = index;
    })
  }

  @Builder
  TabBuilder(tabType: TabBarType) {
    // Tab构建器实现
  }
}

// ✅ 功能视图组件
@Component({ freezeWhenInactive: true })
export struct PracticesView {
  viewModel: PracticeViewModel = PracticeViewModel.getInstance();
  @StorageProp('GlobalInfoModel') globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
  @State practiceState: PracticeState = this.viewModel.getState();

  aboutToAppear(): void {
    this.viewModel.sendEvent({
      type: PracticeEventType.LOAD_SAMPLE_PAGE,
      param: new LoadSamplePageParam()
    });
  }

  build() {
    BaseHomeView({
      bannerState: this.practiceState.bannerState,
      topNavigationData: this.practiceState.topNavigationData,
      contentView: () => {
        this.ContentBuilder()
      }
    })
  }

  @Builder
  ContentBuilder() {
    // 内容构建器
  }
}
```

## 网络和集成规范

### 1. 网络请求

**Web组件集成**:
```typescript
// ✅ Web内容加载
WebUtil.createWebNode(url, this.getUIContext(), NestedScrollMode.SELF_ONLY);

// ✅ 清理资源
aboutToDisappear(): void {
  WebUtil.removeNode(this.sampleCard.originalUrl);
}
```

**网络权限配置**:
```json
// hmos_web_config.json
{
  "whitelist": "{\"UrlPermissionList\":[{\"scheme\":\"https\",\"host\":\"developer.huawei.com\"}]}"
}
```

### 2. 动态模块管理

**Sample下载**:
```typescript
// ✅ 动态模块安装
DynamicInstallManager.fetchModule(ctx, moduleName)
  .then((data: moduleInstallManager.ModuleInstallSessionState) => {
    if (data.code === moduleInstallManager.RequestErrorCode.SUCCESS) {
      this.state.taskId = data.taskId;
    }
  });
```

## 错误处理和日志

### 1. 统一日志管理

**Logger使用**:
```typescript
// ✅ 统一日志格式
const TAG = '[ComponentName]';
Logger.info(TAG, `Operation completed: ${result}`);
Logger.error(TAG, `Operation failed: ${error.message}`);
```

### 2. 错误处理模式

**异常捕获**:
```typescript
// ✅ 完整的错误处理
try {
  const result = await someAsyncOperation();
  Logger.info(TAG, `Success: ${result}`);
} catch (error) {
  const businessError = error as BusinessError;
  Logger.error(TAG, `Error ${businessError.code}: ${businessError.message}`);
  // 用户友好的错误提示
  promptAction.showToast({
    message: '操作失败，请重试',
    duration: 2000
  });
}
```

## 测试规范

### 1. 单元测试

**测试文件组织**:
```
src/ohosTest/ets/
├── test/
│   ├── ComponentTest.ets
│   ├── ViewModelTest.ets
│   └── ServiceTest.ets
└── TestRunner.ets
```

**测试编写模式**:
```typescript
// ✅ 标准测试结构
import { describe, beforeAll, beforeEach, afterEach, afterAll, it, expect } from '@ohos/hypium';

export default function ComponentTest() {
  describe('ComponentTest', () => {
    beforeEach(() => {
      // 测试前准备
    });

    it('should render correctly', () => {
      // 测试实现
      expect(result).assertEqual(expected);
    });
  });
}
```

## 代码审查检查清单

### 必检项目

**架构合规性**:
- [ ] 是否遵循三层架构
- [ ] 依赖方向是否正确
- [ ] 模块类型配置是否正确

**代码质量**:
- [ ] 组件是否正确使用装饰器
- [ ] 状态管理是否合理
- [ ] 错误处理是否完整
- [ ] 日志记录是否规范

**性能考虑**:
- [ ] 是否使用了适当的性能优化
- [ ] 内存使用是否合理
- [ ] 响应式设计是否正确实现

**安全性**:
- [ ] 网络请求是否在白名单内
- [ ] 用户输入是否经过验证
- [ ] 敏感信息是否正确处理

## 常见问题和解决方案

### 1. 构建问题

**内存不足**:
```bash
# 解决方案: 调整hvigor内存配置
"hvigor.pool.maxSize": 3
"ohos.arkCompile.maxSize": 2
```

**依赖冲突**:
```bash
# 解决方案: 检查oh-package.json5依赖版本
# 确保所有模块使用相同的公共依赖版本
```

### 2. 运行时问题

**路由失败**:
```typescript
// 解决方案: 检查router_map.json配置
// 确保buildFunction名称与实际导出一致
```

**状态同步问题**:
```typescript
// 解决方案: 使用@Watch监听状态变化
@StorageProp('GlobalInfoModel') @Watch('handleStateChange')
globalInfoModel: GlobalInfoModel = AppStorage.get('GlobalInfoModel')!;
```

这份规范确保了所有LLMs在HarmonyOS开发中保持一致的代码风格、架构模式和最佳实践。
