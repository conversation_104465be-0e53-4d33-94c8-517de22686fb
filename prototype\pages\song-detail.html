<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>歌曲诗卷 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .song-detail-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .song-cover-large {
            width: 320px;
            height: 320px;
            border-radius: 24px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 5rem;
            box-shadow: 0 24px 80px rgba(0, 0, 0, 0.2);
            position: relative;
            overflow: hidden;
            margin: 0 auto;
        }
        
        .song-cover-large::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: rotate 4s linear infinite;
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .song-info-card {
            background: var(--card-bg);
            border-radius: 24px;
            padding: 2.5rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-top: 2rem;
        }
        
        .lyrics-container {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            max-height: 400px;
            overflow-y: auto;
            position: relative;
        }
        
        .lyrics-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .lyrics-line {
            padding: 0.75rem 0;
            border-bottom: 1px solid var(--border-color);
            transition: all 0.3s ease;
            cursor: pointer;
            writing-mode: horizontal-tb;
            text-align: center;
            font-size: 1.125rem;
            line-height: 1.8;
        }
        
        .lyrics-line:hover {
            background: var(--bg-secondary);
            color: var(--color-primary);
        }
        
        .lyrics-line:last-child {
            border-bottom: none;
        }
        
        .lyrics-line.active {
            background: linear-gradient(90deg, var(--color-primary)/10, var(--color-accent)/10);
            color: var(--color-primary);
            font-weight: 600;
        }
        
        .tech-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-top: 1.5rem;
        }
        
        .tech-info-item {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 1rem;
            border: 1px solid var(--border-color);
            text-align: center;
        }
        
        .tech-info-label {
            font-size: 0.75rem;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }
        
        .tech-info-value {
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
        }
        
        .action-buttons {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
            flex-wrap: wrap;
        }
        
        .action-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.875rem 1.5rem;
            border-radius: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            flex: 1;
            min-width: 140px;
            justify-content: center;
        }
        
        .action-button.primary {
            background: var(--color-primary);
            color: white;
        }
        
        .action-button.primary:hover {
            background: var(--color-accent);
            transform: translateY(-2px);
        }
        
        .action-button.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .action-button.secondary:hover {
            background: var(--card-bg);
            transform: translateY(-2px);
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="song-detail-container water-bg" x-data="songDetailApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">歌曲诗卷</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="shareSong()" class="header-button" aria-label="分享歌曲">
                        <iconify-icon icon="material-symbols:share" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button @click="downloadSong()" class="header-button" aria-label="下载歌曲">
                        <iconify-icon icon="material-symbols:download" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 歌曲信息区域 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 歌曲封面 -->
                <div class="song-cover-large mb-8">
                    <iconify-icon icon="material-symbols:music-note" x-show="!songInfo.cover"></iconify-icon>
                    <img :src="songInfo.cover" :alt="songInfo.title" class="w-full h-full object-cover rounded-3xl" x-show="songInfo.cover">
                </div>
                
                <!-- 歌曲信息卡片 -->
                <div class="song-info-card">
                    <div class="text-center mb-6">
                        <h2 class="text-3xl font-bold text-primary mb-3" x-text="songInfo.title"></h2>
                        <p class="text-xl text-secondary mb-2" x-text="songInfo.artist"></p>
                        <p class="text-lg text-secondary" x-text="songInfo.album"></p>
                    </div>
                    
                    <!-- 技术信息 -->
                    <div class="tech-info-grid">
                        <div class="tech-info-item">
                            <div class="tech-info-label">时长</div>
                            <div class="tech-info-value" x-text="formatTime(songInfo.duration)"></div>
                        </div>
                        <div class="tech-info-item">
                            <div class="tech-info-label">比特率</div>
                            <div class="tech-info-value" x-text="songInfo.bitrate + ' kbps'"></div>
                        </div>
                        <div class="tech-info-item">
                            <div class="tech-info-label">采样率</div>
                            <div class="tech-info-value" x-text="songInfo.sampleRate + ' Hz'"></div>
                        </div>
                        <div class="tech-info-item">
                            <div class="tech-info-label">文件大小</div>
                            <div class="tech-info-value" x-text="formatFileSize(songInfo.fileSize)"></div>
                        </div>
                        <div class="tech-info-item">
                            <div class="tech-info-label">发行年份</div>
                            <div class="tech-info-value" x-text="songInfo.year"></div>
                        </div>
                        <div class="tech-info-item">
                            <div class="tech-info-label">风格</div>
                            <div class="tech-info-value" x-text="songInfo.genre"></div>
                        </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="action-buttons">
                        <button @click="playSong()" class="action-button primary">
                            <iconify-icon icon="material-symbols:play-arrow" class="text-lg"></iconify-icon>
                            播放歌曲
                        </button>
                        
                        <button @click="toggleFavorite()" class="action-button secondary">
                            <iconify-icon :icon="songInfo.isFavorite ? 'material-symbols:anchor' : 'material-symbols:anchor-outline'" class="text-lg"></iconify-icon>
                            <span x-text="songInfo.isFavorite ? '已收藏' : '收藏'"></span>
                        </button>
                        
                        <button @click="addToPlaylist()" class="action-button secondary">
                            <iconify-icon icon="material-symbols:playlist-add" class="text-lg"></iconify-icon>
                            列表
                        </button>
                    </div>
                </div>
                
                <!-- 歌词展示 -->
                <div class="mt-8">
                    <h3 class="text-xl font-semibold text-primary mb-4">歌词</h3>
                    <div class="lyrics-container">
                        <template x-for="(line, index) in lyrics" :key="index">
                            <div @click="seekToLine(index)" 
                                 class="lyrics-line"
                                 :class="currentLyricIndex === index ? 'active' : ''"
                                 x-text="line.text"></div>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function songDetailApp() {
            return {
                songInfo: {
                    title: '水调歌头',
                    artist: '古风音乐',
                    album: '诗词歌赋',
                    duration: 245,
                    bitrate: 320,
                    sampleRate: 44100,
                    fileSize: 9.8 * 1024 * 1024, // 9.8MB
                    year: 2024,
                    genre: '古风/民族',
                    cover: null,
                    isFavorite: true
                },
                
                lyrics: [
                    { text: '明月几时有', time: 0 },
                    { text: '把酒问青天', time: 5 },
                    { text: '不知天上宫阙', time: 10 },
                    { text: '今夕是何年', time: 15 },
                    { text: '我欲乘风归去', time: 20 },
                    { text: '又恐琼楼玉宇', time: 25 },
                    { text: '高处不胜寒', time: 30 },
                    { text: '起舞弄清影', time: 35 },
                    { text: '何似在人间', time: 40 }
                ],
                
                currentLyricIndex: 0,
                
                playSong() {
                    console.log('🎵 播放歌曲:', this.songInfo.title);
                    window.location.href = './player.html';
                },
                
                toggleFavorite() {
                    this.songInfo.isFavorite = !this.songInfo.isFavorite;
                    console.log(this.songInfo.isFavorite ? '⚓ 歌曲已收藏' : '○ 取消收藏歌曲');
                },
                
                addToPlaylist() {
                    console.log('📝 列表');
                },
                
                shareSong() {
                    console.log('📤 分享歌曲:', this.songInfo.title);
                },
                
                downloadSong() {
                    console.log('📥 下载歌曲:', this.songInfo.title);
                },
                
                seekToLine(index) {
                    this.currentLyricIndex = index;
                    console.log('🎯 跳转到歌词:', this.lyrics[index].text);
                },
                
                formatTime(seconds) {
                    const mins = Math.floor(seconds / 60);
                    const secs = Math.floor(seconds % 60);
                    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                },
                
                formatFileSize(bytes) {
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    if (bytes === 0) return '0 B';
                    const i = Math.floor(Math.log(bytes) / Math.log(1024));
                    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('📜 歌曲详情页面已加载');
                    
                    // 模拟歌词同步
                    setInterval(() => {
                        if (this.currentLyricIndex < this.lyrics.length - 1) {
                            this.currentLyricIndex++;
                        } else {
                            this.currentLyricIndex = 0;
                        }
                    }, 5000);
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 歌曲详情');
        });
    </script>
</body>
</html>
