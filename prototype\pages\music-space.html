<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>听汀空间 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .music-space-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .space-card {
            background: var(--card-bg);
            border-radius: 24px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .space-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
        }
        
        .space-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .radar-chart-container {
            position: relative;
            height: 300px;
            margin: 2rem 0;
        }
        
        .music-map {
            background: var(--bg-secondary);
            border-radius: 16px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            position: relative;
            overflow: hidden;
            min-height: 200px;
        }
        
        .map-point {
            position: absolute;
            width: 12px;
            height: 12px;
            background: var(--color-accent);
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(212, 175, 55, 0.4);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .map-point:hover {
            transform: scale(1.5);
            box-shadow: 0 4px 16px rgba(212, 175, 55, 0.6);
        }
        
        .map-point::after {
            content: attr(data-song);
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--card-bg);
            color: var(--text-primary);
            padding: 0.5rem;
            border-radius: 8px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.3s ease;
            border: 1px solid var(--border-color);
        }
        
        .map-point:hover::after {
            opacity: 1;
        }
        
        .music-card {
            background: var(--card-bg);
            border-radius: 16px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .music-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 32px rgba(0, 0, 0, 0.15);
        }
        
        .music-card-icon {
            font-size: 3rem;
            color: var(--color-primary);
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        
        .music-card:hover .music-card-icon {
            transform: scale(1.1);
            color: var(--color-accent);
        }
        
        .share-card {
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
            border-radius: 20px;
            padding: 2rem;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .share-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            animation: shimmer 3s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0%, 100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .qr-code {
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 12px;
            margin: 1rem auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-primary);
            font-size: 3rem;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 1rem;
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }
        
        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 0.5rem;
        }
        
        .stat-label {
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="music-space-container water-bg" x-data="musicSpaceApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">听汀空间</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="exportSpace()" class="header-button" aria-label="导出音乐空间">
                        <iconify-icon icon="material-symbols:download" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 音乐空间内容 -->
        <div class="px-6 py-8">
            <div class="max-w-6xl mx-auto space-y-8">
                <!-- 音乐品味雷达图 -->
                <div class="space-card">
                    <h2 class="text-2xl font-bold text-primary mb-4 text-center">听汀星图</h2>
                    <div class="radar-chart-container">
                        <canvas id="tasteRadar" width="400" height="300"></canvas>
                    </div>
                    <p class="text-center text-secondary">基于你的听歌习惯生成的个性化音乐品味分析</p>
                </div>
                
                <!-- 听汀足迹 -->
                <div class="space-card">
                    <h2 class="text-2xl font-bold text-primary mb-4">听汀足迹</h2>
                    <div class="music-map">
                        <div class="map-point" style="top: 20%; left: 30%;" data-song="水调歌头"></div>
                        <div class="map-point" style="top: 40%; left: 60%;" data-song="静夜思"></div>
                        <div class="map-point" style="top: 70%; left: 25%;" data-song="春江花月夜"></div>
                        <div class="map-point" style="top: 30%; left: 80%;" data-song="将进酒"></div>
                        <div class="map-point" style="top: 60%; left: 45%;" data-song="蒹葭"></div>
                        
                        <!-- 地图背景装饰 -->
                        <div class="absolute inset-0 opacity-10">
                            <iconify-icon icon="material-symbols:map" class="text-6xl text-primary absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"></iconify-icon>
                        </div>
                    </div>
                    <p class="text-center text-secondary mt-4">你的音乐发现之旅轨迹</p>
                </div>
                
                <!-- 听汀统计 -->
                <div class="space-card">
                    <h2 class="text-2xl font-bold text-primary mb-4">听汀统计</h2>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-number" x-text="musicStats.totalSongs"></div>
                            <div class="stat-label">收藏歌曲</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" x-text="musicStats.totalTime"></div>
                            <div class="stat-label">总听歌时长</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" x-text="musicStats.favoriteGenre"></div>
                            <div class="stat-label">最爱风格</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number" x-text="musicStats.discoveredArtists"></div>
                            <div class="stat-label">发现艺术家</div>
                        </div>
                    </div>
                </div>
                
                <!-- 音乐成就 -->
                <div class="space-card">
                    <h2 class="text-2xl font-bold text-primary mb-6">音乐成就</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <template x-for="achievement in achievements" :key="achievement.id">
                            <div class="music-card" :class="achievement.unlocked ? '' : 'opacity-50'">
                                <iconify-icon :icon="achievement.icon" class="music-card-icon"></iconify-icon>
                                <h4 class="font-semibold text-primary mb-2" x-text="achievement.title"></h4>
                                <p class="text-sm text-secondary" x-text="achievement.description"></p>
                                <div x-show="achievement.unlocked" class="mt-2">
                                    <span class="text-xs text-accent font-medium">已解锁</span>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- 分享音乐名片 -->
                <div class="share-card">
                    <h2 class="text-2xl font-bold mb-4">听汀名片</h2>
                    <p class="mb-4">分享你的音乐品味给朋友们</p>
                    
                    <div class="qr-code">
                        <iconify-icon icon="material-symbols:qr-code" class="text-4xl"></iconify-icon>
                    </div>
                    
                    <div class="flex justify-center space-x-4 mt-6">
                        <button @click="shareToSocial('wechat')" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors">
                            <iconify-icon icon="material-symbols:share" class="mr-2"></iconify-icon>
                            微信
                        </button>
                        <button @click="shareToSocial('weibo')" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors">
                            <iconify-icon icon="material-symbols:share" class="mr-2"></iconify-icon>
                            微博
                        </button>
                        <button @click="saveAsImage()" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors">
                            <iconify-icon icon="material-symbols:image" class="mr-2"></iconify-icon>
                            保存图片
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function musicSpaceApp() {
            return {
                musicStats: {
                    totalSongs: 1247,
                    totalTime: '125小时',
                    favoriteGenre: '古风',
                    discoveredArtists: 89
                },
                
                achievements: [
                    {
                        id: 1,
                        title: '音乐探索者',
                        description: '发现50位新艺术家',
                        icon: 'material-symbols:explore',
                        unlocked: true
                    },
                    {
                        id: 2,
                        title: '收藏达人',
                        description: '收藏1000首歌曲',
                        icon: 'material-symbols:anchor',
                        unlocked: true
                    },
                    {
                        id: 3,
                        title: '夜猫子',
                        description: '深夜听歌100小时',
                        icon: 'material-symbols:nightlight',
                        unlocked: false
                    },
                    {
                        id: 4,
                        title: '品味独特',
                        description: '听遍10种音乐风格',
                        icon: 'material-symbols:auto-awesome',
                        unlocked: true
                    }
                ],
                
                getChartColor(colorVar, alpha = 1) {
                    // 获取CSS变量的颜色值
                    const color = getComputedStyle(document.documentElement).getPropertyValue(`--color-${colorVar}`).trim();
                    if (color) {
                        // 如果是hex颜色，转换为rgba
                        if (color.startsWith('#')) {
                            const r = parseInt(color.slice(1, 3), 16);
                            const g = parseInt(color.slice(3, 5), 16);
                            const b = parseInt(color.slice(5, 7), 16);
                            return `rgba(${r}, ${g}, ${b}, ${alpha})`;
                        }
                        // 如果已经是rgb/rgba格式，直接返回或修改透明度
                        if (color.startsWith('rgb')) {
                            if (alpha !== 1) {
                                const rgbMatch = color.match(/\d+/g);
                                if (rgbMatch && rgbMatch.length >= 3) {
                                    return `rgba(${rgbMatch[0]}, ${rgbMatch[1]}, ${rgbMatch[2]}, ${alpha})`;
                                }
                            }
                            return color;
                        }
                    }

                    // 备用颜色
                    const fallbackColors = {
                        'primary': alpha !== 1 ? `rgba(74, 144, 226, ${alpha})` : '#4A90E2',
                        'accent': alpha !== 1 ? `rgba(255, 193, 7, ${alpha})` : '#FFC107',
                        'card-bg': alpha !== 1 ? `rgba(255, 255, 255, ${alpha})` : '#ffffff'
                    };

                    return fallbackColors[colorVar] || (alpha !== 1 ? `rgba(74, 144, 226, ${alpha})` : '#4A90E2');
                },

                initRadarChart() {
                    const ctx = document.getElementById('tasteRadar').getContext('2d');
                    new Chart(ctx, {
                        type: 'radar',
                        data: {
                            labels: ['古风', '民谣', '流行', '摇滚', '古典', '爵士', '电子', '说唱'],
                            datasets: [{
                                label: '音乐偏好',
                                data: [95, 80, 60, 40, 85, 70, 30, 20],
                                borderColor: this.getChartColor('primary'),
                                backgroundColor: this.getChartColor('primary', 0.2),
                                pointBackgroundColor: this.getChartColor('accent'),
                                pointBorderColor: this.getChartColor('card-bg'),
                                pointBorderWidth: 2,
                                pointRadius: 6
                            }]
                        },
                        options: {
                            responsive: true,
                            maintainAspectRatio: false,
                            plugins: {
                                legend: {
                                    display: false
                                }
                            },
                            scales: {
                                r: {
                                    beginAtZero: true,
                                    max: 100,
                                    grid: {
                                        color: 'var(--border-color)'
                                    },
                                    pointLabels: {
                                        color: 'var(--text-primary)',
                                        font: {
                                            size: 14
                                        }
                                    },
                                    ticks: {
                                        display: false
                                    }
                                }
                            }
                        }
                    });
                },
                
                shareToSocial(platform) {
                    console.log('📤 分享到', platform);
                },
                
                saveAsImage() {
                    console.log('💾 保存音乐名片为图片');
                },
                
                exportSpace() {
                    console.log('📊 导出音乐空间数据');
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('🌌 音乐空间页面已加载');
                    
                    // 初始化雷达图
                    this.$nextTick(() => {
                        this.initRadarChart();
                    });
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 音乐空间');
        });
    </script>
</body>
</html>
