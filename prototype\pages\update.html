<!DOCTYPE html>
<html lang="zh-CN" class="theme-morning">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>检查更新 - 听汀</title>
    
    <!-- CDN引入 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <script src="https://unpkg.com/iconify-icon@3.0.0/dist/iconify-icon.min.js"></script>
    
    <!-- 全局样式 -->
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link rel="stylesheet" href="../assets/css/responsive.css">
    <link rel="stylesheet" href="../assets/css/visual-guidelines.css">
    
    <!-- Tailwind 配置 -->
    <script src="../assets/js/tailwind.config.js"></script>
    
    <!-- 动画系统 -->
    <script src="../assets/js/animations.js"></script>
    
    <style>
        .update-container {
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            min-height: 100vh;
        }
        
        .update-card {
            background: var(--card-bg);
            border-radius: 20px;
            padding: 2rem;
            border: 1px solid var(--border-color);
            backdrop-filter: blur(10px);
            margin-bottom: 2rem;
            position: relative;
            overflow: hidden;
        }
        
        .update-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
        }
        
        .version-comparison {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 2rem;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .version-box {
            text-align: center;
            padding: 2rem;
            border-radius: 16px;
            border: 2px solid var(--border-color);
            background: var(--bg-secondary);
            position: relative;
        }
        
        .version-box.current {
            border-color: var(--color-primary);
            background: var(--card-bg);
        }
        
        .version-box.latest {
            border-color: var(--color-accent);
            background: linear-gradient(135deg, var(--color-primary)/5, var(--color-accent)/5);
        }
        
        .version-number {
            font-size: 2rem;
            font-weight: 700;
            color: var(--color-primary);
            margin-bottom: 0.5rem;
        }
        
        .version-label {
            color: var(--text-secondary);
            font-size: 0.875rem;
            margin-bottom: 1rem;
        }
        
        .version-status {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .version-status.current {
            background: var(--color-primary);
            color: white;
        }
        
        .version-status.available {
            background: var(--color-accent);
            color: white;
        }
        
        .arrow-icon {
            font-size: 2rem;
            color: var(--color-accent);
            animation: pulse 2s ease-in-out infinite;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 0.6; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.1); }
        }
        
        .update-progress {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
            margin: 2rem 0;
        }
        
        .progress-bar {
            width: 100%;
            height: 12px;
            background: var(--border-color);
            border-radius: 6px;
            overflow: hidden;
            margin: 1rem 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--color-primary), var(--color-accent));
            border-radius: 6px;
            transition: width 0.3s ease;
            position: relative;
        }
        
        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s ease-in-out infinite;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        
        .progress-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.875rem;
            color: var(--text-secondary);
        }
        
        .changelog {
            max-height: 400px;
            overflow-y: auto;
        }
        
        .changelog-item {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
            position: relative;
        }
        
        .changelog-item::before {
            content: '';
            position: absolute;
            left: 0.75rem;
            top: 3rem;
            bottom: -1rem;
            width: 2px;
            background: var(--border-color);
        }
        
        .changelog-item:last-child::before {
            display: none;
        }
        
        .changelog-date {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            flex-shrink: 0;
            position: relative;
            z-index: 1;
        }
        
        .changelog-content {
            flex: 1;
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .changelog-version {
            font-size: 1.125rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
        }
        
        .changelog-features {
            list-style: none;
            padding: 0;
        }
        
        .changelog-features li {
            position: relative;
            padding-left: 1.5rem;
            margin-bottom: 0.5rem;
            color: var(--text-secondary);
        }
        
        .changelog-features li::before {
            content: '✨';
            position: absolute;
            left: 0;
        }
        
        .update-actions {
            display: flex;
            gap: 1rem;
            margin-top: 2rem;
        }
        
        .action-button {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 1rem 2rem;
            border-radius: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            flex: 1;
            justify-content: center;
        }
        
        .action-button.primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-accent));
            color: white;
        }
        
        .action-button.primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(74, 144, 226, 0.3);
        }
        
        .action-button.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }
        
        .action-button.secondary:hover {
            background: var(--card-bg);
            transform: translateY(-2px);
        }
        
        .action-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }
        
        .settings-section {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 1.5rem;
            border: 1px solid var(--border-color);
        }
        
        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 1rem 0;
            border-bottom: 1px solid var(--border-color);
        }
        
        .setting-item:last-child {
            border-bottom: none;
        }
        
        .toggle-switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: var(--bg-primary);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .toggle-switch.active {
            background: var(--color-primary);
        }
        
        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .toggle-switch.active .toggle-slider {
            transform: translateX(24px);
        }
    </style>
</head>
<body class="font-primary">
    <script src="../assets/js/theme.js"></script>
    
    <div class="update-container water-bg" x-data="updateApp()">
        <!-- 顶部导航栏 -->
        <header class="header-container glass-morphism p-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <button @click="goBack()" class="header-button" aria-label="返回">
                        <iconify-icon icon="material-symbols:arrow-back" class="text-lg"></iconify-icon>
                    </button>
                    <h1 class="header-brand-text">检查更新</h1>
                </div>
                
                <div class="header-actions">
                    <button @click="checkForUpdates()" class="header-button" aria-label="检查更新">
                        <iconify-icon icon="material-symbols:refresh" class="text-lg"></iconify-icon>
                    </button>
                    
                    <button data-theme-toggle class="header-button" aria-label="切换主题">
                        <iconify-icon icon="material-symbols:light-mode" class="theme-icon text-lg"></iconify-icon>
                    </button>
                </div>
            </div>
        </header>
        
        <!-- 更新内容 -->
        <div class="px-6 py-8">
            <div class="max-w-4xl mx-auto">
                <!-- 版本对比 -->
                <div class="update-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">版本信息</h2>
                    
                    <div class="version-comparison">
                        <div class="version-box current">
                            <div class="version-number" x-text="currentVersion"></div>
                            <div class="version-label">当前版本</div>
                            <div class="version-status current">
                                <iconify-icon icon="material-symbols:check-circle" class="text-sm"></iconify-icon>
                                已安装
                            </div>
                        </div>
                        
                        <iconify-icon icon="material-symbols:arrow-forward" class="arrow-icon"></iconify-icon>
                        
                        <div class="version-box latest">
                            <div class="version-number" x-text="latestVersion"></div>
                            <div class="version-label">最新版本</div>
                            <div class="version-status available" x-show="hasUpdate">
                                <iconify-icon icon="material-symbols:new-releases" class="text-sm"></iconify-icon>
                                可更新
                            </div>
                            <div class="version-status current" x-show="!hasUpdate">
                                <iconify-icon icon="material-symbols:check-circle" class="text-sm"></iconify-icon>
                                已是最新
                            </div>
                        </div>
                    </div>
                    
                    <!-- 更新操作 -->
                    <div class="update-actions">
                        <button @click="startUpdate()" 
                                class="action-button primary" 
                                :disabled="!hasUpdate || isUpdating"
                                x-show="hasUpdate">
                            <iconify-icon icon="material-symbols:download" class="text-lg"></iconify-icon>
                            <span x-text="isUpdating ? '更新中...' : '立即更新'"></span>
                        </button>
                        
                        <button @click="checkForUpdates()" 
                                class="action-button secondary"
                                :disabled="isChecking">
                            <iconify-icon icon="material-symbols:refresh" class="text-lg"></iconify-icon>
                            <span x-text="isChecking ? '检查中...' : '检查更新'"></span>
                        </button>
                    </div>
                    
                    <!-- 更新进度 -->
                    <div x-show="isUpdating" class="update-progress">
                        <div class="progress-info">
                            <span x-text="updateStatus"></span>
                            <span x-text="updateProgress + '%'"></span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" :style="`width: ${updateProgress}%`"></div>
                        </div>
                        <div class="progress-info">
                            <span x-text="downloadSpeed"></span>
                            <span x-text="timeRemaining"></span>
                        </div>
                    </div>
                </div>
                
                <!-- 更新日志 -->
                <div class="update-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">更新日志</h2>
                    
                    <div class="changelog">
                        <template x-for="log in changelog" :key="log.version">
                            <div class="changelog-item">
                                <div class="changelog-date">
                                    <div class="text-xs" x-text="log.month"></div>
                                    <div class="text-lg" x-text="log.day"></div>
                                </div>
                                <div class="changelog-content">
                                    <div class="changelog-version" x-text="'v' + log.version"></div>
                                    <ul class="changelog-features">
                                        <template x-for="feature in log.features" :key="feature">
                                            <li x-text="feature"></li>
                                        </template>
                                    </ul>
                                </div>
                            </div>
                        </template>
                    </div>
                </div>
                
                <!-- 更新设置 -->
                <div class="update-card">
                    <h2 class="text-xl font-semibold text-primary mb-6">更新设置</h2>
                    
                    <div class="settings-section">
                        <div class="setting-item">
                            <div>
                                <h3 class="font-medium text-primary">自动检查更新</h3>
                                <p class="text-sm text-secondary">启动时自动检查新版本</p>
                            </div>
                            <div class="toggle-switch" :class="settings.autoCheck ? 'active' : ''" @click="settings.autoCheck = !settings.autoCheck">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div>
                                <h3 class="font-medium text-primary">自动下载更新</h3>
                                <p class="text-sm text-secondary">发现新版本时自动下载</p>
                            </div>
                            <div class="toggle-switch" :class="settings.autoDownload ? 'active' : ''" @click="settings.autoDownload = !settings.autoDownload">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div>
                                <h3 class="font-medium text-primary">包含预发布版本</h3>
                                <p class="text-sm text-secondary">检查Beta版本和预览版</p>
                            </div>
                            <div class="toggle-switch" :class="settings.includeBeta ? 'active' : ''" @click="settings.includeBeta = !settings.includeBeta">
                                <div class="toggle-slider"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function updateApp() {
            return {
                currentVersion: '3.0.0',
                latestVersion: '3.1.0',
                hasUpdate: true,
                isChecking: false,
                isUpdating: false,
                updateProgress: 0,
                updateStatus: '准备下载...',
                downloadSpeed: '',
                timeRemaining: '',
                
                settings: {
                    autoCheck: true,
                    autoDownload: false,
                    includeBeta: false
                },
                
                changelog: [
                    {
                        version: '3.1.0',
                        month: '7月',
                        day: '20',
                        features: [
                            '新增桌面小组件功能',
                            '优化音质增强算法',
                            '修复播放列表同步问题',
                            '改进用户界面响应速度'
                        ]
                    },
                    {
                        version: '3.0.0',
                        month: '7月',
                        day: '19',
                        features: [
                            '全新的中国风界面设计',
                            '新增音乐空间个性化展示',
                            '增加数据备份和恢复功能',
                            '完善隐私设置和用户协议'
                        ]
                    },
                    {
                        version: '2.5.0',
                        month: '7月',
                        day: '15',
                        features: [
                            '新增均衡器功能',
                            '支持更多音频格式',
                            '优化内存使用',
                            '修复已知问题'
                        ]
                    }
                ],
                
                async checkForUpdates() {
                    this.isChecking = true;
                    
                    try {
                        // 模拟检查更新
                        await new Promise(resolve => setTimeout(resolve, 2000));
                        
                        // 随机决定是否有更新
                        this.hasUpdate = Math.random() > 0.3;
                        if (this.hasUpdate) {
                            this.latestVersion = '3.1.0';
                        } else {
                            this.latestVersion = this.currentVersion;
                        }
                        
                        console.log('🔍 检查更新完成:', this.hasUpdate ? '发现新版本' : '已是最新版本');
                        
                    } catch (error) {
                        console.error('检查更新失败:', error);
                    } finally {
                        this.isChecking = false;
                    }
                },
                
                async startUpdate() {
                    this.isUpdating = true;
                    this.updateProgress = 0;
                    
                    const steps = [
                        { status: '连接更新服务器...', progress: 10 },
                        { status: '下载更新包...', progress: 30 },
                        { status: '验证更新包...', progress: 60 },
                        { status: '安装更新...', progress: 80 },
                        { status: '完成更新', progress: 100 }
                    ];
                    
                    for (let i = 0; i < steps.length; i++) {
                        const step = steps[i];
                        this.updateStatus = step.status;
                        
                        // 模拟进度更新
                        const startProgress = this.updateProgress;
                        const targetProgress = step.progress;
                        const duration = 2000; // 2秒
                        const startTime = Date.now();
                        
                        while (this.updateProgress < targetProgress) {
                            const elapsed = Date.now() - startTime;
                            const progress = Math.min(elapsed / duration, 1);
                            this.updateProgress = Math.floor(startProgress + (targetProgress - startProgress) * progress);
                            
                            // 更新下载信息
                            if (step.status.includes('下载')) {
                                this.downloadSpeed = '2.5 MB/s';
                                this.timeRemaining = '剩余 ' + Math.max(1, Math.ceil((100 - this.updateProgress) / 10)) + ' 分钟';
                            }
                            
                            await new Promise(resolve => setTimeout(resolve, 50));
                        }
                        
                        await new Promise(resolve => setTimeout(resolve, 500));
                    }
                    
                    // 更新完成
                    this.currentVersion = this.latestVersion;
                    this.hasUpdate = false;
                    this.isUpdating = false;
                    this.downloadSpeed = '';
                    this.timeRemaining = '';
                    
                    console.log('✅ 更新完成');
                    alert('更新完成！应用将重启以应用更改。');
                },
                
                goBack() {
                    window.history.back();
                },
                
                init() {
                    console.log('🔄 检查更新页面已加载');
                    
                    // 监听主题变化
                    document.addEventListener('themechange', (e) => {
                        console.log('主题已切换:', e.detail.theme);
                    });
                }
            }
        }
        
        document.addEventListener('DOMContentLoaded', () => {
            console.log('🎵 听汀 - 检查更新');
        });
    </script>
</body>
</html>
